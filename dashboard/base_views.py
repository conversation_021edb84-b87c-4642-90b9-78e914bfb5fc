import locale
import re
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>, BrowsableAPIRenderer
from django.db import connection
from datetime import date, datetime, timedelta
from typing import Tuple, List  # Import for type hints
import logging
from django.utils import translation
from django.utils.translation import activate
from django.utils.translation import get_language, gettext as _
from django.utils.translation import gettext_lazy
import urllib

from dashboard.serializers import AgentMetricSummarySerializer

from .renderers import CustomFilenameXLSXRenderer

class RawSQLQueryAPIView(APIView):
    """
    Base API View for executing a single raw SQL query with time filtering.
    Subclasses must define `serializer_class` and implement `get_sql_query`.
    """

    renderer_classes = [J<PERSON><PERSON>ender<PERSON>, BrowsableAPIRenderer, CustomFilenameXLSXRenderer]
    serializer_class = None
    filename_title = gettext_lazy("Default Report") # Add this for makemessages
    date_column_name = "created_on"
    filter_conditions = ""
    extra_params = []

    def get_serializer_class(self):
        assert self.serializer_class is not None, (
            "'%s' should either include a `serializer_class` attribute, "
            "or override the `get_serializer_class()` method."
            % self.__class__.__name__
        )
        return self.serializer_class

    def get_serializer(self, *args, **kwargs):
        serializer_class = self.get_serializer_class()
        kwargs['context'] = self.get_serializer_context()
        return serializer_class(*args, **kwargs)

    def get_serializer_context(self):
        return {
            'request': self.request,
            'format': self.format_kwarg,
            'view': self
        }
    
    def get_query_filters(self, start_date: date, end_date: date) -> Tuple[str, List]:
        """
        Debug version that prints the actual filter being used.
        """
        filter_clause = (
            f" AND {self.date_column_name} >= %s AND {self.date_column_name} < %s"
        )
        end_date_exclusive = end_date + timedelta(days=1)
        params = [start_date, end_date_exclusive]
        
        print(f"DEBUG: Filtering {self.date_column_name} >= {start_date} AND {self.date_column_name} < {end_date_exclusive}")
        
        return filter_clause, params

    def get(self, request, *args, **kwargs):
        default_end_date = date(2025, 7, 12)
        default_start_date = default_end_date - timedelta(days=6)

        user_start_date_str = request.query_params.get("start_date")
        user_end_date_str = request.query_params.get("end_date")

        self.start_date = "unknown_start"
        self.end_date = "unknown_end"

        lang = request.query_params.get('lang', 'en')
        translation.activate(lang)
        
        try:
            if user_start_date_str:
                start_date = datetime.strptime(user_start_date_str, "%Y-%m-%d").date()
            else:
                start_date = default_start_date

            if user_end_date_str:
                end_date = datetime.strptime(user_end_date_str, "%Y-%m-%d").date()
            else:
                end_date = default_end_date

            self.start_date = start_date.isoformat()
            self.end_date = end_date.isoformat()
            
        except ValueError:
            return Response(
                {
                    "error": _("Invalid date format. Use YYYY-MM-DD for start_date and end_date.")
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date > end_date:
            return Response(
                {"error": _("start_date cannot be after end_date.")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        time_filter_clause, params_from_get_query_filters = self.get_query_filters(
            start_date, end_date
        )

        if not self.serializer_class:
            return Response(
                {"error": _("serializer_class is not defined for this view.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        try:
            sql_query, params_for_execution = self.get_sql_query(
                time_filter_clause, params_from_get_query_filters
            )

            with connection.cursor() as cursor:
                cursor.execute(sql_query, params_for_execution)
                columns = [col[0] for col in cursor.description]
                data = [dict(zip(columns, row)) for row in cursor.fetchall()]

            serializer = self.serializer_class(data, many=True)
            return Response(serializer.data)

        except NotImplementedError:
            return Response(
                {"error": _("get_sql_query() not implemented by subclass.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logging.error(f"Database query error in {self.__class__.__name__}: {e}")
            return Response(
                {"error": _(f"An internal server error occurred: {str(e)}")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            
    def get_renderer_context(self):
        context = super().get_renderer_context()
        filename_start_date = getattr(self, "start_date", "unknown_start")
        filename_end_date = getattr(self, "end_date", "unknown_end")
        
        # Here we only provide the filename components, not the full encoded string.
        context["filename_title"] = str(self.filename_title)
        context["start_date"] = filename_start_date
        context["end_date"] = filename_end_date
        return context



# --- TIME COMPARISON BASE CLASS
class RawSQLMetricComparisonAPIView(APIView):
    """
    A comprehensive base API View for comparing a single metric (count, sum, average, etc.)
    between two time periods using raw SQL. It provides time-series data and percentage change.
    """

    renderer_classes = [JSONRenderer, BrowsableAPIRenderer, CustomFilenameXLSXRenderer]

    table_name = None
    date_column_name = "created_on"
    filter_conditions = ""
    extra_params = []
    serializer_class = None
    time_series_granularity = "day"
    units = "items"
    metric_aggregate_sql = "COUNT(*)"

    def get_serializer_class(self):
        assert self.serializer_class is not None, (
            "'%s' should either include a `serializer_class` attribute, "
            "or override the `get_serializer_class()` method."
            % self.__class__.__name__
        )
        return self.serializer_class

    def get_serializer(self, *args, **kwargs):
        serializer_class = self.get_serializer_class()
        kwargs['context'] = self.get_serializer_context()
        return serializer_class(*args, **kwargs)

    def get_serializer_context(self):
        return {
            'request': self.request,
            'format': self.format_kwarg,
            'view': self
        }

    def get_query_filters(self, start_date: date, end_date: date) -> Tuple[str, List]:
        """
        Debug version of get_query_filters with logging.
        """
        filter_clause = (
            f" AND DATE({self.date_column_name}) >= %s AND DATE({self.date_column_name}) <= %s"
        )
        params = [start_date, end_date]
        
        # Debug logging
        print(f"DEBUG get_query_filters: start_date={start_date}, end_date={end_date}")
        print(f"DEBUG filter_clause: {filter_clause}")
        print(f"DEBUG params: {params}")
        
        return filter_clause, params

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> Tuple[str, List]:
        sql = f"""
        SELECT
            {self.metric_aggregate_sql} AS metric_value
        FROM
            {self.table_name}
        WHERE 1=1 {time_filter_clause} {self.filter_conditions};
        """
        # The params here should match the placeholders in the SQL query *exactly*.
        # Since get_query_filters now uses `DATE(...) <= %s`, we don't need to add a day.
        params = [start_date, end_date] + self.extra_params
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> Tuple[str, List]:
        sql = f"""
          SELECT
            DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name}) AS date,
            {self.metric_aggregate_sql} AS value
          FROM
            {self.table_name}
          WHERE
            1=1 {time_filter_clause} {self.filter_conditions}
          GROUP BY
            DATE_TRUNC('{self.time_series_granularity}', {self.date_column_name})
          ORDER BY
            date;
        """
        # The params here should match the placeholders in the SQL query *exactly*.
        # Since get_query_filters now uses `DATE(...) <= %s`, we don't need to add a day.
        params = [start_date, end_date] + self.extra_params
        return sql, params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> Tuple[str, List]:
        return (
            "",
            [],
        )

    def get(self, request, *args, **kwargs):
        default_end_date = date(2025, 7, 12)
        default_start_date = default_end_date - timedelta(days=6)

        user_start_date_str = request.query_params.get("start_date")
        user_end_date_str = request.query_params.get("end_date")

        lang = request.query_params.get('lang', 'en')
        translation.activate(lang)

        try:
            if user_start_date_str:
                start_date = datetime.strptime(user_start_date_str, "%Y-%m-%d").date()
            else:
                start_date = default_start_date

            if user_end_date_str:
                end_date = datetime.strptime(user_end_date_str, "%Y-%m-%d").date()
            else:
                end_date = default_end_date

            self.start_date = start_date.isoformat()
            self.end_date = end_date.isoformat()

        except ValueError:
            return Response(
                {
                    "error": "Invalid date format. Use YYYY-MM-DD for start_date and end_date."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date > end_date:
            return Response(
                {"error": "start_date cannot be after end_date."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        duration_timedelta = end_date - start_date
        if duration_timedelta > timedelta(days=364):
            self.time_series_granularity = "month"
        elif duration_timedelta > timedelta(days=30):
            self.time_series_granularity = "week"
        else:
            self.time_series_granularity = "day"

        main_period_filter_clause, main_period_params_for_query_filters = self.get_query_filters(
            start_date, end_date
        )

        duration_days = (end_date - start_date).days
        comparison_end_date = start_date - timedelta(days=1)
        comparison_start_date = comparison_end_date - timedelta(days=duration_days)

        comparison_period_filter_clause, comparison_period_params_for_query_filters = (
            self.get_query_filters(comparison_start_date, comparison_end_date)
        )

        if not self.table_name:
            return Response(
                {"error": "table_name is not defined for this view."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        if not self.serializer_class:
            return Response(
                {"error": "serializer_class is not defined for this view."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        response_data = {}

        self.metric_value_name = re.sub(r'\([^()]*\)', '', self.metric_aggregate_sql)

        try:
            with connection.cursor() as cursor:
                main_metric_sql, main_metric_params_for_exec = (
                    self.get_single_period_metric_sql(
                        main_period_filter_clause, start_date, end_date
                    )
                )
                cursor.execute(main_metric_sql, main_metric_params_for_exec)
                main_metric_row = cursor.fetchone()

                current_metric_value = None
                if main_metric_row and main_metric_row[0] is not None:
                    current_metric_value = float(main_metric_row[0])

                comparison_metric_sql, comparison_metric_params_for_exec = (
                    self.get_single_period_metric_sql(
                        comparison_period_filter_clause,
                        comparison_start_date,
                        comparison_end_date,
                    )
                )
                cursor.execute(comparison_metric_sql, comparison_metric_params_for_exec)
                comparison_metric_row = cursor.fetchone()

                previous_metric_value = None
                if comparison_metric_row and comparison_metric_row[0] is not None:
                    previous_metric_value = float(comparison_metric_row[0])

                main_time_series_sql, main_time_series_params_for_exec = (
                    self.get_time_series_metric_sql(
                        main_period_filter_clause, start_date, end_date
                    )
                )
                cursor.execute(main_time_series_sql, main_time_series_params_for_exec)
                main_time_series_rows = cursor.fetchall()
                main_time_series_data = [
                    {
                        "date": row[0].isoformat(),
                        "value": float(row[1]) if row[1] is not None else None,
                    }
                    for row in main_time_series_rows
                ]

                comparison_time_series_sql, comparison_time_series_params_for_exec = (
                    self.get_time_series_metric_sql(
                        comparison_period_filter_clause,
                        comparison_start_date,
                        comparison_end_date,
                    )
                )
                cursor.execute(
                    comparison_time_series_sql, comparison_time_series_params_for_exec
                )
                comparison_time_series_rows = cursor.fetchall()
                comparison_time_series_data = [
                    {
                        "date": row[0].isoformat(),
                        "value": float(row[1]) if row[1] is not None else None,
                    }
                    for row in comparison_time_series_rows
                ]

                main_detailed_data_sql, main_detailed_params_for_exec = (
                    self.get_detailed_data_sql(
                        main_period_filter_clause, start_date, end_date
                    )
                )
                main_detailed_data = []
                if (main_detailed_data_sql):
                    cursor.execute(
                        main_detailed_data_sql, main_detailed_params_for_exec
                    )
                    if cursor.description:
                        detailed_columns = [col[0] for col in cursor.description]
                        for row in cursor.fetchall():
                            row_dict = dict(zip(detailed_columns, row))
                            for key, value in row_dict.items():
                                if isinstance(value, (datetime, date)):
                                    row_dict[key] = value.isoformat()
                            main_detailed_data.append(row_dict)
                    else:
                        print(f"WARNING: get_detailed_data_sql returned a query but cursor.description was None. Query: {main_detailed_data_sql}")

            percentage_change = None

            if current_metric_value is None and previous_metric_value is None:
                percentage_change = None
            elif previous_metric_value is None or previous_metric_value == 0.0:
                if current_metric_value is not None and current_metric_value > 0.0:
                    percentage_change = None
                else:
                    percentage_change = 0.0
            elif current_metric_value is None:
                percentage_change = -100.0
            else:
                percentage_change = round(
                    ((current_metric_value - previous_metric_value) / previous_metric_value) * 100.0,
                    2,
                )

            response_data = {
                "main_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "metric_value_name": self.metric_value_name,
                    "metric_value": current_metric_value,
                    "time_series_data": main_time_series_data,
                    "detailed_tickets": main_detailed_data,
                },
                "comparison_period": {
                    "start_date": comparison_start_date.isoformat(),
                    "end_date": comparison_end_date.isoformat(),
                    "metric_value_name": self.metric_value_name,
                    "metric_value": previous_metric_value,
                    "time_series_data": comparison_time_series_data,
                },
                "percentage_change": percentage_change,
                "units": self.units,
            }

            serializer = self.serializer_class(response_data)
            serialized_data = serializer.data

        except NotImplementedError as e:
            return Response(
                {"error": f"Missing implementation for required SQL method: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            print(f"Database query error in {self.__class__.__name__}: {e}")
            return Response(
                {"error": f"An internal server error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        
        return Response(serialized_data)


    def get_renderer_context(self):
        context = super().get_renderer_context()
        filename_start_date = getattr(self, "start_date", "unknown_start")
        filename_end_date = getattr(self, "end_date", "unknown_end")
        
        context["filename_title"] = str(self.filename_title)
        context["start_date"] = filename_start_date
        context["end_date"] = filename_end_date
        return context
    
class RawSQLMetricComparisonPerAgentAPIView(RawSQLMetricComparisonAPIView):
    """
    Extended API View that inherits from RawSQLMetricComparisonAPIView to get metrics
    for EACH AGENT, comparing current period to previous period.
    
    Adds agent-specific functionality while leveraging the base class infrastructure.

    Subclasses must additionally implement:
    - `get_all_agents_sql() -> Tuple[str, List]`: Returns SQL to fetch all agents (id, username).
    - Override the base methods to accept agent_id parameter:
      - `get_single_period_metric_sql(time_filter_clause, start_date, end_date, agent_id=None)`
      - `get_time_series_metric_sql(time_filter_clause, start_date, end_date, agent_id=None)`
    """

    renderer_classes = [JSONRenderer, BrowsableAPIRenderer, CustomFilenameXLSXRenderer]

    def get_all_agents_sql(self) -> tuple[str, list]:
        """
        Subclasses MUST implement this to return SQL to fetch all agents.
        The SQL should return columns aliased as 'id' and 'username'.
        Example: SELECT id, username FROM user_user;
        """
        raise NotImplementedError("Subclasses must implement get_all_agents_sql()")

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int = None
    ) -> tuple[str, list]:
        """
        Extended version that accepts agent_id parameter.
        Subclasses should override this to handle agent-specific queries.
        """
        if agent_id is None:
            # Fall back to parent implementation for backwards compatibility
            return super().get_single_period_metric_sql(time_filter_clause, start_date, end_date)
        else:
            raise NotImplementedError(
                "Subclasses must implement get_single_period_metric_sql() with agent_id support"
            )

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int = None
    ) -> tuple[str, list]:
        """
        Extended version that accepts agent_id parameter.
        Subclasses should override this to handle agent-specific queries.
        """
        if agent_id is None:
            # Fall back to parent implementation for backwards compatibility
            return super().get_time_series_metric_sql(time_filter_clause, start_date, end_date)
        else:
            raise NotImplementedError(
                "Subclasses must implement get_time_series_metric_sql() with agent_id support"
            )

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int = None
    ) -> tuple[str, list]:
        """
        Extended version that accepts agent_id parameter.
        Default implementation returns empty (no detailed data).
        """
        return ("", [])

    def get(self, request, *args, **kwargs):
        # Use parent's date parsing and validation logic
        default_end_date = date(2025, 7, 12)
        default_start_date = default_end_date - timedelta(days=6)

        user_start_date_str = request.query_params.get("start_date")
        user_end_date_str = request.query_params.get("end_date")
        filter_agent_ids_str = request.query_params.get("agent_ids")

        lang = request.query_params.get('lang', 'en')
        translation.activate(lang)

        # Dynamically set metric_value_name based on current metric_aggregate_sql
        self.metric_value_name = re.sub(r'\([^()]*\)', '', self.metric_aggregate_sql)

        try:
            if user_start_date_str:
                start_date = datetime.strptime(user_start_date_str, "%Y-%m-%d").date()
            else:
                start_date = default_start_date

            if user_end_date_str:
                end_date = datetime.strptime(user_end_date_str, "%Y-%m-%d").date()
            else:
                end_date = default_end_date

            self.start_date = start_date.isoformat()
            self.end_date = end_date.isoformat()

        except ValueError:
            return Response(
                {
                    "error": "Invalid date format. Use YYYY-MM-DD for start_date and end_date."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date > end_date:
            return Response(
                {"error": "start_date cannot be after end_date."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Use parent's granularity logic
        duration_timedelta = end_date - start_date
        if duration_timedelta > timedelta(days=364):
            self.time_series_granularity = "month"
        elif duration_timedelta > timedelta(days=30):
            self.time_series_granularity = "week"
        else:
            self.time_series_granularity = "day"

        # Calculate comparison period dates
        duration_days = (end_date - start_date).days
        comparison_end_date = start_date - timedelta(days=1)
        comparison_start_date = comparison_end_date - timedelta(days=duration_days)

        # Get common time filter clauses using parent method
        main_period_filter_clause, main_period_params = self.get_query_filters(
            start_date, end_date
        )
        comparison_period_filter_clause, comparison_period_params = (
            self.get_query_filters(comparison_start_date, comparison_end_date)
        )

        if not self.table_name:
            return Response(
                {"error": "table_name is not defined for this view."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


        try:
            with connection.cursor() as cursor:
                # 1. Fetch all agents (or a filtered subset)
                all_agents_sql, all_agents_params = self.get_all_agents_sql()
                cursor.execute(all_agents_sql, all_agents_params)
                all_agents_rows = cursor.fetchall()

                # Filter agents if agent_ids query param is provided
                filtered_agents = []
                if filter_agent_ids_str:
                    try:
                        requested_agent_ids = [
                            int(aid.strip())
                            for aid in filter_agent_ids_str.split(",")
                            if aid.strip()
                        ]
                        for agent_row in all_agents_rows:
                            agent_id = agent_row[0]  # Assuming first column is ID
                            agent_username = agent_row[1]  # Assuming second column is username
                            if agent_id in requested_agent_ids:
                                filtered_agents.append(
                                    {"id": agent_id, "username": agent_username}
                                )
                    except ValueError:
                        print(
                            "Warning: Invalid agent_ids provided in query params. Returning all agents."
                        )
                        filtered_agents = [
                            {"id": row[0], "username": row[1]}
                            for row in all_agents_rows
                        ]
                else:
                    filtered_agents = [
                        {"id": row[0], "username": row[1]} for row in all_agents_rows
                    ]

                if not filtered_agents:
                    return Response(
                        {"message": "No agents found or selected."},
                        status=status.HTTP_200_OK,
                    )

                # 2. Process metrics for each agent
                all_agents_metrics = []
                for agent_info in filtered_agents:
                    agent_id = agent_info["id"]
                    agent_name = agent_info["username"]

                    # Main Period Metric
                    main_metric_sql, main_metric_params_for_exec = (
                        self.get_single_period_metric_sql(
                            main_period_filter_clause, start_date, end_date, agent_id
                        )
                    )
                    cursor.execute(main_metric_sql, main_metric_params_for_exec)
                    main_metric_row = cursor.fetchone()
                    current_metric_value = (
                        float(main_metric_row[0])
                        if main_metric_row and main_metric_row[0] is not None
                        else 0.0
                    )

                    # Comparison Period Metric
                    comparison_metric_sql, comparison_metric_params_for_exec = (
                        self.get_single_period_metric_sql(
                            comparison_period_filter_clause,
                            comparison_start_date,
                            comparison_end_date,
                            agent_id,
                        )
                    )
                    cursor.execute(
                        comparison_metric_sql, comparison_metric_params_for_exec
                    )
                    comparison_metric_row = cursor.fetchone()
                    previous_metric_value = (
                        float(comparison_metric_row[0])
                        if comparison_metric_row
                        and comparison_metric_row[0] is not None
                        else 0.0
                    )

                    # Main Period Time Series
                    main_time_series_sql, main_time_series_params_for_exec = (
                        self.get_time_series_metric_sql(
                            main_period_filter_clause, start_date, end_date, agent_id
                        )
                    )
                    cursor.execute(
                        main_time_series_sql, main_time_series_params_for_exec
                    )
                    main_time_series_rows = cursor.fetchall()
                    main_time_series_data = [
                        {
                            "date": row[0].isoformat(),
                            "value": float(row[1]) if row[1] is not None else 0.0,
                        }
                        for row in main_time_series_rows
                    ]

                    # Comparison Period Time Series
                    (
                        comparison_time_series_sql,
                        comparison_time_series_params_for_exec,
                    ) = self.get_time_series_metric_sql(
                        comparison_period_filter_clause,
                        comparison_start_date,
                        comparison_end_date,
                        agent_id,
                    )
                    cursor.execute(
                        comparison_time_series_sql,
                        comparison_time_series_params_for_exec,
                    )
                    comparison_time_series_rows = cursor.fetchall()
                    comparison_time_series_data = [
                        {
                            "date": row[0].isoformat(),
                            "value": float(row[1]) if row[1] is not None else 0.0,
                        }
                        for row in comparison_time_series_rows
                    ]

                    # Calculate Percentage Change using improved logic from parent
                    percentage_change = self._calculate_percentage_change(
                        current_metric_value, previous_metric_value
                    )

                    # Assemble individual agent's response data
                    agent_response_data = {
                        "agent_id": agent_id,
                        "agent_name": agent_name,
                        "main_period": {
                            "start_date": start_date.isoformat(),
                            "end_date": end_date.isoformat(),
                            "metric_value_name": self.metric_value_name,
                            "metric_value": current_metric_value,
                            "time_series_data": main_time_series_data,
                        },
                        "comparison_period": {
                            "start_date": comparison_start_date.isoformat(),
                            "end_date": comparison_end_date.isoformat(),
                            "metric_value_name": self.metric_value_name,
                            "metric_value": previous_metric_value,
                            "time_series_data": comparison_time_series_data,
                        },
                        "percentage_change": percentage_change,
                        "units": self.units,
                    }
                    all_agents_metrics.append(agent_response_data)

            # Serialize the list of agent metrics
            serializer = AgentMetricSummarySerializer(all_agents_metrics, many=True)
            return Response(serializer.data)

        except NotImplementedError as e:
            return Response(
                {"error": f"Missing implementation for required SQL method: {e}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            print(f"Database query error in {self.__class__.__name__}: {e}")
            return Response(
                {"error": f"An internal server error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _calculate_percentage_change(self, current_value: float, previous_value: float) -> float:
        """
        Calculate percentage change with proper handling of edge cases.
        Uses the improved logic from the parent class.
        """
        
        if previous_value == 0.0:
            return 0.0 if current_value == 0.0 else None  # Return infinity for division by zero when current_value > 0
        
        percentage_change = round(
            ((current_value - previous_value) / previous_value) * 100.0,
            2,
        )

        return percentage_change

    def get_renderer_context(self):
        """
        Extend parent's renderer context for per-agent CSV downloads.
        """
        context = super().get_renderer_context()

        # Override filename to indicate per-agent nature
        filename_start_date = getattr(self, "start_date", "unknown_start")
        filename_end_date = getattr(self, "end_date", "unknown_end")
        metric_name_for_filename = getattr(self, "metric_value_name", "data")

        context["csv_filename"] = (
            f"{metric_name_for_filename}_per_agent_from_{filename_start_date}_to_{filename_end_date}.csv"
        )

        return context