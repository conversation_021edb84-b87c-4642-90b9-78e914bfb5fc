from rest_framework import serializers
from django.utils.translation import gettext_lazy as _, ngettext


##### [Agent Performance] ######
class TimeSeriesDataPointSerializer(serializers.Serializer):
    date = serializers.DateField()
    value = serializers.FloatField(allow_null=True)


# Differs for Time Periods and no time periods
class MessagePeriodDataSerializer(serializers.Serializer):
    start_date = serializers.DateField(label=_("Start Date"))
    end_date = serializers.DateField(label=_("End Date"))
    metric_value_name = serializers.CharField(allow_null=True, required=False, label=_("Metric Name"))
    metric_value = serializers.FloatField(allow_null=True, label=_("Metric Value"))
    # time_series_data = serializers.ListField(child=TimeSeriesDataPointSerializer())


class MessageTimeSeriesDataSerializer(serializers.Serializer):
    start_date = serializers.DateField(label=_("Start Date"))
    end_date = serializers.DateField(label=_("End Date"))
    metric_value_name = serializers.CharField(allow_null=True, required=False, label=_("Metric Name"))
    metric_value = serializers.FloatField(allow_null=True, label=_("Metric Value"))
    time_series_data = serializers.ListField(child=TimeSeriesDataPointSerializer())


class AgentPerformanceSummarySerializer(serializers.Serializer):
    """
    Serializer for the combined agent performance summary.
    Supports multiple languages through Django's internationalization.
    """
    
    agent_name = serializers.CharField(allow_null=True, label=_("Agent Name"))
    avg_response_time_minutes = serializers.FloatField(
        allow_null=True,
        label=_("Average Response Time (Minutes)")
    )
    avg_handling_time_minutes = serializers.FloatField(
        allow_null=True,
        label=_("Average Handling Time (Minutes)")
    )
    average_csat_score = serializers.FloatField(
        allow_null=True,
        label=_("Average CSAT Score")
    )
    units = serializers.CharField(
        max_length=50,
        allow_null=True,
        label=_("Unit")
    )


class AgentMetricSummarySerializer(serializers.Serializer):
    """
    Serializer for a single agent's metric summary, including comparison data.
    """

    # agent_id = serializers.IntegerField(allow_null=True, label=_("Agent ID"))
    agent_name = serializers.CharField(allow_null=True, label=_("Agent Name"))
    main_period = MessagePeriodDataSerializer(label=_("Main Period"))
    comparison_period = MessagePeriodDataSerializer(label=_("Comparison Period"))
    percentage_change = serializers.FloatField(label=_("Percentage Change"))
    units = serializers.CharField(max_length=50, label=_("Unit"))


class TicketStatusSummarySerializer(serializers.Serializer):
    status = serializers.CharField(label=_("Status"))
    ticket_count = serializers.IntegerField(source="จำนวนทิกเก็ต", label=_("Ticket Count"))


class ComprehensiveAgentPerformanceSerializer(serializers.Serializer):
    """
    Serializer for the comprehensive agent performance summary.
    Matches the columns from the final combined SQL query.
    """
    
    agent_name = serializers.CharField(source="เจ้าหน้าที่", allow_null=True, label=_("Agent"))
    closed_tickets = serializers.IntegerField(source="ทิกเก็ตที่ปิดแล้ว", allow_null=True, label=_("Closed Tickets"))
    unclosed_tickets = serializers.IntegerField(source="ทิกเก็ตที่ยังไม่ปิด", allow_null=True, label=_("Unclosed Tickets"))
    avg_response_time_minutes = serializers.FloatField(
        source="ตอบกลับเฉลี่ย (นาที)", allow_null=True, label=_("Average Response Time (Minutes)")
    )
    avg_handling_time_minutes = serializers.FloatField(
        source="เวลาจัดการเฉลี่ย (นาที)", allow_null=True, label=_("Average Handling Time (Minutes)")
    )
    avg_csat_score = serializers.FloatField(source="CSAT เฉลี่ย (เต็ม 5)", allow_null=True, label=_("Average CSAT (out of 5)"))


##### [Chat Performance] #####
class MessageSummarySerializer(serializers.Serializer):
    main_period = MessagePeriodDataSerializer(label=_("Main Period"))
    comparison_period = MessagePeriodDataSerializer(label=_("Comparison Period"))
    percentage_change = serializers.FloatField(label=_("Percentage Change"))
    units = serializers.CharField(max_length=50, label=_("Unit"))


class MessageTimeSeriesSerializer(serializers.Serializer):
    main_period = MessageTimeSeriesDataSerializer(label=_("Main Period"))
    comparison_period = MessageTimeSeriesDataSerializer(label=_("Comparison Period"))
    percentage_change = serializers.FloatField(label=_("Percentage Change"))
    units = serializers.CharField(max_length=50, label=_("Unit"))


class OverdueTicketSerializer(serializers.Serializer):
    ticket_number = serializers.CharField(source="เลขทิกเก็ต", label=_("Ticket Number"))
    status = serializers.CharField(source="สถานะทิกเก็ต", allow_null=True, label=_("Ticket Status"))
    customer = serializers.CharField(source="ลูกค้า", allow_null=True, label=_("Customer"))
    priority = serializers.CharField(source="ความสำคัญ", allow_null=True, label=_("Priority"))
    sentiment = serializers.CharField(source="ความรู้สึก", allow_null=True, label=_("Sentiment"))
    agent = serializers.CharField(source="เจ้าหน้าที่", allow_null=True, label=_("Agent"))
    created_time = serializers.DateTimeField(source="เวลาที่สร้าง", allow_null=True, label=_("Created Time"))
    closed_time = serializers.DateTimeField(source="เวลาที่ปิด", allow_null=True, label=_("Closed Time"))
    overdue_time = serializers.DurationField(source="เวลาที่ใช้", allow_null=True, label=_("Time Taken"))
    overdue_time_char = serializers.SerializerMethodField( allow_null=True, label=_("Formatted Time"))


    def get_overdue_time_char(self, obj):
        duration = obj.get('เวลาที่ใช้')
        if not duration:
            return None
            
        days = duration.days
        hours, remainder = divmod(duration.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        parts = []
        if days:
            # Correct ngettext usage
            parts.append(ngettext("{num} day", "{num} days", days).format(num=days))
        if hours:
            # Correct ngettext usage
            parts.append(ngettext("{num} hour", "{num} hours", hours).format(num=hours))
        if minutes:
            # Correct ngettext usage
            parts.append(ngettext("{num} minute", "{num} minutes", minutes).format(num=minutes))
            
        return ", ".join(parts) or _("0 minutes")  # Use _() not _gettext()


class ClosedTicketsByCaseTypeSerializer(serializers.Serializer):
    """
    Serializer for the summary of closed tickets by case type.
    """

    case_type = serializers.CharField(source="ประเภทเคส", label=_("Case Type"))
    ticket_count = serializers.IntegerField(source="จำนวนทิกเก็ต", label=_("Ticket Count"))


class ClosedTicketsByCaseTopicSerializer(serializers.Serializer):
    """
    Serializer for the summary of closed tickets by case topic.
    """

    case_topic = serializers.CharField(source="หัวข้อเคสย่อย", label=_("Case Topic"))
    ticket_count = serializers.IntegerField(source="จำนวนทิกเก็ต", label=_("Ticket Count"))


class ClosedTicketsByCaseTypeAndTopicSerializer(serializers.Serializer):
    """
    Serializer for the summary of closed tickets by both case type and case topic.
    """

    case_type = serializers.CharField(source="ประเภทเคส", label=_("Case Type"))
    case_topic = serializers.CharField(source="หัวข้อย่อย", label=_("Case Topic"))
    ticket_count = serializers.IntegerField(source="จำนวนทิกเก็ต", label=_("Ticket Count"))


##### [Response Time Volume] #####
class IncomingMessageCountTimeSeriesSerializer(serializers.Serializer):
    """
    Serializer for the time series of incoming message counts.
    """

    time = serializers.DateField(label=_("Time"))
    incoming_message_count = serializers.IntegerField(source="จำนวนข้อความขาเข้า", label=_("Incoming Message Count"))


class TicketCategoryBreakdownSerializer(serializers.Serializer):
    """
    Serializer for the breakdown of ticket counts by category.
    """

    category = serializers.CharField(label=_("Category"))
    ticket_count = serializers.IntegerField(label=_("Ticket Count"))


class CustomerMessageHeatmapSerializer(serializers.Serializer):
    """
    Serializer for the heatmap of customer message counts by hour and day of week.
    """

    time_slot = serializers.CharField(source="ช่วงเวลา", label=_("Time Slot"))
    monday = serializers.IntegerField(source="จันทร์", label=_("Monday"))
    tuesday = serializers.IntegerField(source="อังคาร", label=_("Tuesday"))
    wednesday = serializers.IntegerField(source="พุธ", label=_("Wednesday"))
    thursday = serializers.IntegerField(source="พฤหัสบดี", label=_("Thursday"))
    friday = serializers.IntegerField(source="ศุกร์", label=_("Friday"))
    saturday = serializers.IntegerField(source="เสาร์", label=_("Saturday"))
    sunday = serializers.IntegerField(source="อาทิตย์", label=_("Sunday"))


##### [Work Quality] #####
class ResponderResponseTimeSerializer(serializers.Serializer):
    """
    Serializer for the average response time for agents and chatbots.
    """

    responder_type = serializers.CharField(label=_("Responder Type"))
    total_count = serializers.IntegerField(label=_("Total Count"))
    raw_avg = serializers.FloatField(allow_null=True, label=_("Average Response Time (Seconds)"))


class SentimentAnalysisSummarySerializer(serializers.Serializer):
    """
    Serializer for the summary of sentiment analysis counts.
    """

    time = serializers.DateTimeField(label=_("Time"))
    positive = serializers.IntegerField(source="Positive", label=_("Positive"))
    neutral = serializers.IntegerField(source="Neutral", label=_("Neutral"))
    negative = serializers.IntegerField(source="Negative", label=_("Negative"))


class SentimentAnalysisTimeSeriesSerializer(serializers.Serializer):
    """
    Serializer for the time series of sentiment analysis counts.
    """

    time = serializers.DateField(label=_("Time"))
    positive = serializers.IntegerField(source="Positive", label=_("Positive"))
    neutral = serializers.IntegerField(source="Neutral", label=_("Neutral"))
    negative = serializers.IntegerField(source="Negative", label=_("Negative"))


class SentimentAnalysisByCaseTypeSerializer(serializers.Serializer):
    """
    Serializer for sentiment analysis counts grouped by case type.
    """

    case_type = serializers.CharField(source="ประเภทเคส", label=_("Case Type"))
    positive = serializers.IntegerField(source="Positive", label=_("Positive"))
    neutral = serializers.IntegerField(source="Neutral", label=_("Neutral"))
    negative = serializers.IntegerField(source="Negative", label=_("Negative"))


class CSATScoreTimeSeriesSerializer(serializers.Serializer):
    """
    Serializer for the time series of average CSAT scores.
    """

    time = serializers.DateField(label=_("Time"))
    average_csat_score = serializers.FloatField(source="ความพึงพอใจเฉลี่ย", label=_("Average CSAT Score"))