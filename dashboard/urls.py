from django.urls import path, include
from . import grafana
from . import views

from .views import (
    #### Agents Performance APIView ####
    AgentPerformanceSummaryAPIView,
    AgentPreviousAssignmentCountAPIView,
    AgentAssignedTicketsCountAPIView,
    AgentResponseRateWithin5MinAPIView,
    ComprehensiveAgentPerformanceAPIView,
    #### Chat Performance APIView ####
    IncomingMessageCountComparisonAPIView,
    TicketStatusCountAPIView,
    DistinctActiveTicketsCountAPIView,
    ClosedTicketCountAPIView,
    AverageResponseTimeAPIView,
    AverageHandlingTimeAPIView,
    HandlingRateWithin5MinAPIView,
    AgentResponseWithin6SecondsRateAPIView,
    ClosedTicketRateAPIView,
    OverdueUnclosedTicketsAPIView,
    OverdueClosedTicketsAPIView,
    ClosedTicketsByCaseTypeAPIView,
    ClosedTicketsByCaseTopicAPIView,
    ClosedTicketsByCaseTypeAndTopicAPIView,
    #### Response Time Value APIView ####
    IncomingTicketCountAPIView,
    TicketCategoryTotalCountAPIView,
    IncomingMessageCountTimeSeriesAPIView,
    CustomerMessageHeatmapAPIView,
    #### Work Quality APIView ####
    CSATScoreTimeSeriesAPIView,
    FirstResponseTimeAPIView,
    AverageResponseTimeAPIView,
    ResponderResponseTimeAPIView,
    SentimentAnalysisSummaryAPIView,
    SentimentAnalysisTimeSeriesAPIView,
    SentimentAnalysisByCaseTypeAPIView,
)

urlpatterns = [
    # path(
    #     "api/dashboard/<int:dashboard_id>/",
    #     grafana.GetDashboard.as_view(),
    #     name="get-dashboard",
    # ),
    ##########################################################################################
    # URLs for the [Agent Performance] dashboard API endpoints
    ##########################################################################################
    path(
        "api/agent-performance-summary/",
        AgentPerformanceSummaryAPIView.as_view(),
        name="agent_performance_summary",
    ),
    path(
        "api/agent-previous-assignment-count/",
        AgentPreviousAssignmentCountAPIView.as_view(),
        name="agent_previous_assignment_count",
    ),
    path(
        "api/agent-assigned-tickets-count/",
        AgentAssignedTicketsCountAPIView.as_view(),
        name="agent_assigned_tickets_count",
    ),
    path(
        "api/agent-response-rate-within-5min/",
        AgentResponseRateWithin5MinAPIView.as_view(),
        name="agent_response_rate_within_5min",
    ),
    path(
        "api/comprehensive-agent-performance/",
        ComprehensiveAgentPerformanceAPIView.as_view(),
        name="comprehensive_agent_performance",
    ),
    ##########################################################################################
    # URLs for the [Chat Performance] dashboard API endpoints
    ##########################################################################################
    #  1. จำนวนข้อความขาเข้าทั้งหมด 
    path(
        "api/incoming-message-count/",
        IncomingMessageCountComparisonAPIView.as_view(),
        name="incoming_message_count",
    ),
    #  2. จำนวนทิกเก็ตทั้งหมดของเจ้าหน้าที่ 
    path(
        "api/distinct-incoming-tickets-count/",
        DistinctActiveTicketsCountAPIView.as_view(),
        name="distinct_incoming_tickets_count",
    ),
    #  3. จำนวนทิกเก็ตทั้งหมดที่ปิดแล้วของเจ้าหน้าที่ 
    path(
        "api/closed-ticket-count/",
        ClosedTicketCountAPIView.as_view(),
        name="closed_ticket_count",
    ),
    # 4.  อัตราการปิดของเจ้าหน้าที่ เทียบขาเข้า (%)  
    path(
        "api/closed-ticket-rate/",
        ClosedTicketRateAPIView.as_view(),
        name="closed_ticket_rate",
    ),
    #  5. เวลาตอบกลับเฉลี่ยของเจ้าหน้าที่ (วินาที) 
    path(
        "api/average-response-time/",
        AverageResponseTimeAPIView.as_view(),
        name="average_response_time",
    ),
    # 6. อัตราการตอบกลับภายใน 6 วินาที (%) 
    path(
        "api/6second-response-rate/",
        AgentResponseWithin6SecondsRateAPIView.as_view(),
        name="agent_respond_rate_with_6second",
    ),
    #  7. เวลาจัดการเฉลี่ยของเจ้าหน้าที่ (นาที)
    path(
        "api/average-handling-time/",
        AverageHandlingTimeAPIView.as_view(),
        name="average_handling_time",
    ),
    #  8. อัตราการจัดการภายใน 5 นาที (%) 
    path(
        "api/handling-rate-within-5min/",
        HandlingRateWithin5MinAPIView.as_view(),
        name="handling_rate_within_5min",
    ),
    # 9. จำนวนทิกเก็ตของเจ้าหน้าที่ รายสถานะ ณ ขณะนี้
    path(
        "api/ticket-status-count/",
        TicketStatusCountAPIView.as_view(),
        name="ticket_status_count",
    ),
    #  10. ทิกเก็ตทั้งหมดที่ยังไม่ปิด: ค้างมากกว่า 1 วัน
    path(
        "api/overdue-unclosed-tickets/",
        OverdueUnclosedTicketsAPIView.as_view(),
        name="overdue_unclosed_tickets",
    ),
    #  11. ทิกเก็ตทั้งหมดที่ปิดแล้ว: ค้างมากกว่า 1 วัน
    path(
        "api/overdue-closed-tickets/",
        OverdueClosedTicketsAPIView.as_view(),
        name="overdue_closed_tickets",
    ),
    #  12. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคส
    path(
        "api/closed-tickets-by-case-type/",
        ClosedTicketsByCaseTypeAPIView.as_view(),
        name="closed_tickets_by_case_type",
    ),
    # 13. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: หัวข้อเคสย่อย
    path(
        "api/closed-tickets-by-case-topic/",
        ClosedTicketsByCaseTopicAPIView.as_view(),
        name="closed_tickets_by_case_topic",
    ),
    # 14. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคสและหัวข้อย่อย
    path(
        "api/closed-tickets-by-case-type-and-topic/",
        ClosedTicketsByCaseTypeAndTopicAPIView.as_view(),
        name="closed_tickets_by_case_type_and_topic",
    ),
    ##########################################################################################
    # URLs for the [Response Time Volume] dashboard API endpoints
    ##########################################################################################
    # First One use the same URLs as
    # path(
    #     "api/incoming-message-count/",
    #     IncomingMessageCountComparisonAPIView.as_view(),
    #     name="ticket_status_count",
    # ),
     
    path( 
        "api/ticket-total-count/",
        IncomingTicketCountAPIView.as_view(),
        name="ticket_total_count",
    ),
    path(
        "api/ticket-category-total-count/",
        TicketCategoryTotalCountAPIView.as_view(),
        name="ticket_category_total_count",
    ),
    path( #TimeSeries
        "api/incoming-message-count-time-series/",
        IncomingMessageCountTimeSeriesAPIView.as_view(),
        name="incoming_message_count_time_series",
    ),
    path(
        "api/customer-message-heatmap/",
        CustomerMessageHeatmapAPIView.as_view(),
        name="customer_message_heatmap",
    ),
    ##########################################################################################
    # URLs for the [Work Quality] dashboard API endpoints
    ##########################################################################################
    path( #TimeSeries
        "api/csat-score-time-series/",
        CSATScoreTimeSeriesAPIView.as_view(),
        name="csat_score_time_series",
    ),
    path( #TimeSeries
        "api/first-response-time/",
        FirstResponseTimeAPIView.as_view(),
        name="first_response_time",
    ),
    path( 
        "api/responder-response-time/",
        ResponderResponseTimeAPIView.as_view(),
        name="responder_response_time",
    ),
    path( #TimeSeries
        "api/average-response-time/",
        AverageResponseTimeAPIView.as_view(),
        name="average_response_time",
    ),
    path(
        "api/sentiment-analysis-summary/",
        SentimentAnalysisSummaryAPIView.as_view(),
        name="sentiment_analysis_summary",
    ),
    path(
        "api/sentiment-analysis-time-series/",
        SentimentAnalysisTimeSeriesAPIView.as_view(),
        name="sentiment_analysis_time_series",
    ),
    path(
        "api/sentiment-analysis-by-case-type/",
        SentimentAnalysisByCaseTypeAPIView.as_view(),
        name="sentiment_analysis_by_case_type",
    ),

    # ##########################################################################################
    # ##########################################################################################
    # ##########################################################################################
    # #### URLs for the xlsx Downloads ####
    # ##########################################################################################
    # ##########################################################################################
    # ##########################################################################################
    # # URLs for the [Agent Performance] dashboard API endpoints
    # ##########################################################################################
    path(
        "api/agent-performance-summary.xlsx/",
        AgentPerformanceSummaryAPIView.as_view(),
        {"format": "xlsx"},
        name="agent_performance_summary_xlsx",
    ),
    path(
        "api/agent-previous-assignment-count.xlsx/",
        AgentPreviousAssignmentCountAPIView.as_view(),
        {"format": "xlsx"},
        name="agent_previous_assignment_count_xlsx",
    ),
    path(
        "api/agent-assigned-tickets-count.xlsx/",
        AgentAssignedTicketsCountAPIView.as_view(),
        {"format": "xlsx"},
        name="agent_assigned_tickets_count_xlsx",
    ),
    path(
        "api/agent-response-rate-within-5min.xlsx/",
        AgentResponseRateWithin5MinAPIView.as_view(),
        {"format": "xlsx"},
        name="agent_response_rate_within_5min_xlsx",
    ),
    path(
        "api/comprehensive-agent-performance.xlsx/",
        ComprehensiveAgentPerformanceAPIView.as_view(),
        {"format": "xlsx"},
        name="comprehensive_agent_performance_xlsx",
    ),
    # ##########################################################################################
    # # URLs for the [Chat Performance] dashboard API endpoints
    # ##########################################################################################
    #  1. จำนวนข้อความขาเข้าทั้งหมด 
    path(
        "api/incoming-message-count.xlsx/",
        IncomingMessageCountComparisonAPIView.as_view(),
        {"format": "xlsx"},
        name="incoming_message_count_xlsx",
    ),
    #  2. จำนวนทิกเก็ตทั้งหมดของเจ้าหน้าที่ 
    path(
        "api/distinct-incoming-tickets-count.xlsx/",
        DistinctActiveTicketsCountAPIView.as_view(),
        {"format": "xlsx"},
        name="distinct_incoming_tickets_count_xlsx",
    ),
    #  3. จำนวนทิกเก็ตทั้งหมดที่ปิดแล้วของเจ้าหน้าที่ 
    path(
        "api/closed-ticket-count.xlsx/",
        ClosedTicketCountAPIView.as_view(),
        {"format": "xlsx"},
        name="closed_ticket_count_xlsx",
    ),
    # 4.  อัตราการปิดของเจ้าหน้าที่ เทียบขาเข้า (%)  
    path(
        "api/closed-ticket-rate.xlsx/",
        ClosedTicketRateAPIView.as_view(),
        {"format": "xlsx"},
        name="closed_ticket_rate_xlsx",
    ),
    #  5. เวลาตอบกลับเฉลี่ยของเจ้าหน้าที่ (วินาที) 
    path(
        "api/average-response-time.xlsx/",
        AverageResponseTimeAPIView.as_view(),
        {"format": "xlsx"},
        name="average_response_time_xlsx",
    ),
    # 6. อัตราการตอบกลับภายใน 6 วินาที (%) 
    path(
        "api/6second-response-rate.xlsx/",
        AgentResponseWithin6SecondsRateAPIView.as_view(),
        {"format": "xlsx"},
        name="agent_respond_rate_with_6second_xlsx",
    ),
    #  7. เวลาจัดการเฉลี่ยของเจ้าหน้าที่ (นาที)
    path(
        "api/average-handling-time.xlsx/",
        AverageHandlingTimeAPIView.as_view(),
        {"format": "xlsx"},
        name="average_handling_time_xlsx",
    ),
    #  8. อัตราการจัดการภายใน 5 นาที (%) 
    path(
        "api/handling-rate-within-5min.xlsx/",
        HandlingRateWithin5MinAPIView.as_view(),
        {"format": "xlsx"},
        name="handling_rate_within_5min_xlsx",
    ),
    # 9. จำนวนทิกเก็ตของเจ้าหน้าที่ รายสถานะ ณ ขณะนี้
    path(
        "api/ticket-status-count.xlsx/",
        TicketStatusCountAPIView.as_view(),
        {"format": "xlsx"},
        name="ticket_status_count_xlsx",
    ),
    #  10. ทิกเก็ตทั้งหมดที่ยังไม่ปิด: ค้างมากกว่า 1 วัน
    path(
        "api/overdue-unclosed-tickets.xlsx/",
        OverdueUnclosedTicketsAPIView.as_view(),
        {"format": "xlsx"},
        name="overdue_unclosed_tickets_xlsx",
    ),
    #  11. ทิกเก็ตทั้งหมดที่ปิดแล้ว: ค้างมากกว่า 1 วัน
    path(
        "api/overdue-closed-tickets.xlsx/",
        OverdueClosedTicketsAPIView.as_view(),
        {"format": "xlsx"},
        name="overdue_closed_tickets_xlsx",
    ),
    #  12. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคส
    path(
        "api/closed-tickets-by-case-type.xlsx/",
        ClosedTicketsByCaseTypeAPIView.as_view(),
        {"format": "xlsx"},
        name="closed_tickets_by_case_type_xlsx",
    ),
    # 13. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: หัวข้อเคสย่อย
    path(
        "api/closed-tickets-by-case-topic.xlsx/",
        ClosedTicketsByCaseTopicAPIView.as_view(),
        {"format": "xlsx"},
        name="closed_tickets_by_case_topic_xlsx",
    ),
    # 14. ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคสและหัวข้อย่อย
    path(
        "api/closed-tickets-by-case-type-and-topic.xlsx/",
        ClosedTicketsByCaseTypeAndTopicAPIView.as_view(),
        {"format": "xlsx"},
        name="closed_tickets_by_case_type_and_topic_xlsx",
    ),
    # ##########################################################################################
    # # URLs for the [Response Time Volume] dashboard API endpoints
    # ##########################################################################################
    # # First One use the same URLs as
    # path(
    #     "api/incoming-message-count/",
    #     IncomingMessageCountComparisonAPIView.as_view(),
    #     name="ticket_status_count",
    # ),
    # ADDED
    path(
        "api/ticket-total-count.xlsx/",
        IncomingTicketCountAPIView.as_view(),
        {"format": "xlsx"},
        name="ticket_total_count_xlsx",
    ),
    path(
        "api/ticket-category-total-count.xlsx/",
        TicketCategoryTotalCountAPIView.as_view(),
        {"format": "xlsx"},
        name="ticket_category_total_count_xlsx",
    ),
    path(
        "api/incoming-message-count-time-series.xlsx/",
        IncomingMessageCountTimeSeriesAPIView.as_view(),
        {"format": "xlsx"},
        name="incoming_message_count_time_series_xlsx",
    ),
    path(
        "api/customer-message-heatmap.xlsx/",
        CustomerMessageHeatmapAPIView.as_view(),
        {"format": "xlsx"},
        name="customer_message_heatmap_xlsx",
    ),
    # ##########################################################################################
    # # URLs for the [Work Quality] dashboard API endpoints
    # ##########################################################################################
    path(
        "api/csat-score-time-series.xlsx/",
        CSATScoreTimeSeriesAPIView.as_view(),
        {"format": "xlsx"},
        name="csat_score_time_series_xlsx",
    ),
    path(
        "api/first-response-time.xlsx/",
        FirstResponseTimeAPIView.as_view(),
        {"format": "xlsx"},
        name="first_response_time_xlsx",
    ),
    path(
        "api/responder-response-time.xlsx/",
        ResponderResponseTimeAPIView.as_view(),
        {"format": "xlsx"},
        name="responder_response_time_xlsx",
    ),
    path(
        "api/average-response-time.xlsx/",
        AverageResponseTimeAPIView.as_view(),
        {"format": "xlsx"},
        name="average_response_time_xlsx",
    ),
    path(
        "api/sentiment-analysis-summary.xlsx/",
        SentimentAnalysisSummaryAPIView.as_view(),
        {"format": "xlsx"},
        name="sentiment_analysis_summary_xlsx",
    ),
    path(
        "api/sentiment-analysis-time-series.xlsx/",
        SentimentAnalysisTimeSeriesAPIView.as_view(),
        {"format": "xlsx"},
        name="sentiment_analysis_time_series_xlsx",
    ),
    path(
        "api/sentiment-analysis-by-case-type.xlsx/",
        SentimentAnalysisByCaseTypeAPIView.as_view(),
        {"format": "xlsx"},
        name="sentiment_analysis_by_case_type_xlsx",
    ),
]
