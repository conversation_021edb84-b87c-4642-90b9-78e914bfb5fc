<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n';
    import { currentLanguage } from '$lib/stores/languagePreference';
    import { onMount } from 'svelte';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';

    // Import service and types
    import { dashboardService } from '$lib/api/features/dashboard/dashboard.service';
    import type {
        TicketStatusDataItem,
        CaseTypeDataItem,
        SubCaseTypeDataItem,
        CaseSubCaseInfo,
        ChatPerformanceParams
    } from '$lib/api/types/dashboard';

    // Define props for startDate and endDate
    export let startDate: string | undefined;
    export let endDate: string | undefined;

    // Data fetched from backend - Scorecard values
    let allTickets: number | null = null;
    let allTicketsTrend: number | null = null;
    let allClosedTickets: number | null = null;
    let allClosedTicketsTrend: number | null = null;
    let chatVolume: number | null = null;
    let chatVolumeTrend: number | null = null;
    let closedRate: number | null = null;
    let closedRateTrend: number | null = null;
    let avgHandlingTime: number | null = null;
    let handlingTimeRate: number | null = null;
    let avgResponseTime: number | null = null;
    let responseTimeTrend: number | null = null;
    let responseRateWithin6s: number | null = null;
    let responseRateWithin6sTrend: number | null = null;
    let handlingRateWithin5mins: number | null = null;
    let handlingRateWithin5minsTrend: number | null = null;

    // Data fetched from backend - Charts and tables
    let ticketStatusData: TicketStatusDataItem[] = [];
    let closedTicketsByCaseType: CaseTypeDataItem[] = [];
    let closedTicketsBySubCaseType: SubCaseTypeDataItem[] = [];
    let closedCaseSubCaseTable: CaseSubCaseInfo[] = [];

    // Loading and error states
    let isLoadingTicketStatusChart: boolean = true;
    let isLoadingCaseTypeChart: boolean = true;
    let isLoadingSubCaseTypeChart: boolean = true;
    let isLoadingCaseSubCaseTable: boolean = true;

    // Specific error messages for charts/tables, set to generic for frontend
    let ticketStatusChartError: string | null = null;
    let caseTypeChartError: string | null = null;
    let subCaseTypeChartError: string | null = null;
    let caseSubCaseTableError: string | null = null;

    // Expand state variables for modals
    let isTicketStatusChartExpanded: boolean = false;
    let isCaseTypeChartExpanded: boolean = false;
    let isSubCaseTypeChartExpanded: boolean = false;
    let isCaseSubCaseTableExpanded: boolean = false;



    // SORTING STATE AND FUNCTIONALITY
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        unclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedCaseSubCaseTable: { column: 'count', direction: 'desc' },
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;

                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                return 0;
            });
        }
        return dataArray;
    }



    // Add initialization tracking
    let hasInitialized = false;
    let isInitializing = true;

    // Track previous values to detect actual changes
    let previousStartDate: string | undefined = undefined;
    let previousEndDate: string | undefined = undefined;
    let previousLanguage: string | undefined = undefined;

    async function fetchData() {
        console.log("fetchData called for Chat Performance dashboards/tables");
        console.log(`Current filter settings (from props): startDate='${startDate}', endDate='${endDate}'`);

        isLoadingTicketStatusChart = true;
        isLoadingCaseTypeChart = true;
        isLoadingSubCaseTypeChart = true;
        isLoadingCaseSubCaseTable = true;

        ticketStatusChartError = null;
        caseTypeChartError = null;
        subCaseTypeChartError = null;
        caseSubCaseTableError = null;

        chatVolume = null;
        chatVolumeTrend = null;
        allTickets = null;
        allTicketsTrend = null;
        allClosedTickets = null;
        allClosedTicketsTrend = null;
        closedRate = null;
        closedRateTrend = null;
        avgResponseTime = null;
        responseTimeTrend = null;
        responseRateWithin6s = null;
        responseRateWithin6sTrend = null;
        avgHandlingTime = null;
        handlingTimeRate = null;
        handlingRateWithin5mins = null;
        handlingRateWithin5minsTrend = null;

        try {
            const params: ChatPerformanceParams = {
                startDate,
                endDate
            };

            const data = await dashboardService.fetchChatPerformanceData(params, applyInitialSort);

            // Update component state with fetched data
            chatVolume = data.chatVolume;
            chatVolumeTrend = data.chatVolumeTrend;
            allTickets = data.allTickets;
            allTicketsTrend = data.allTicketsTrend;
            allClosedTickets = data.allClosedTickets;
            allClosedTicketsTrend = data.allClosedTicketsTrend;
            closedRate = data.closedRate;
            closedRateTrend = data.closedRateTrend;
            avgResponseTime = data.avgResponseTime;
            responseTimeTrend = data.responseTimeTrend;
            responseRateWithin6s = data.responseRateWithin6s;
            responseRateWithin6sTrend = data.responseRateWithin6sTrend;
            avgHandlingTime = data.avgHandlingTime;
            handlingTimeRate = data.handlingTimeRate;
            handlingRateWithin5mins = data.handlingRateWithin5mins;
            handlingRateWithin5minsTrend = data.handlingRateWithin5minsTrend;

            ticketStatusData = data.ticketStatusData;
            closedTicketsByCaseType = data.closedTicketsByCaseType;
            closedTicketsBySubCaseType = data.closedTicketsBySubCaseType;
            closedCaseSubCaseTable = data.closedCaseSubCaseTable;

            // Set error states based on data availability
            ticketStatusChartError = data.ticketStatusData.length === 0 ? t('db.noDataAvailable') : null;
            caseTypeChartError = data.closedTicketsByCaseType.length === 0 ? t('db.noDataAvailable') : null;
            subCaseTypeChartError = data.closedTicketsBySubCaseType.length === 0 ? t('db.noDataAvailable') : null;
            caseSubCaseTableError = data.closedCaseSubCaseTable.length === 0 ? t('db.noDataAvailable') : null;

        } catch (error) {
            console.error('Error fetching chat performance data:', error);
            // Reset data on error
            chatVolume = null;
            chatVolumeTrend = null;
            allTickets = null;
            allTicketsTrend = null;
            allClosedTickets = null;
            allClosedTicketsTrend = null;
            closedRate = null;
            closedRateTrend = null;
            avgResponseTime = null;
            responseTimeTrend = null;
            responseRateWithin6s = null;
            responseRateWithin6sTrend = null;
            avgHandlingTime = null;
            handlingTimeRate = null;
            handlingRateWithin5mins = null;
            handlingRateWithin5minsTrend = null;

            ticketStatusData = [];
            closedTicketsByCaseType = [];
            closedTicketsBySubCaseType = [];
            closedCaseSubCaseTable = [];

            // Set error messages
            ticketStatusChartError = t('db.noDataAvailable');
            caseTypeChartError = t('db.noDataAvailable');
            subCaseTypeChartError = t('db.noDataAvailable');
            caseSubCaseTableError = t('db.noDataAvailable');
        } finally {
            // Reset loading states
            isLoadingTicketStatusChart = false;
            isLoadingCaseTypeChart = false;
            isLoadingSubCaseTypeChart = false;
            isLoadingCaseSubCaseTable = false;
        }
    }

    // Excel download functions using service
    async function handleDownloadAgentTicketsByStatusExcel() {
        const params: ChatPerformanceParams = { startDate, endDate };
        await dashboardService.downloadAgentTicketsByStatusExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAgentClosedTicketsByCaseTypeExcel() {
        const params: ChatPerformanceParams = { startDate, endDate };
        await dashboardService.downloadAgentClosedTicketsByCaseTypeExcel(params, (key: string) => t(key));
    }


    async function handleDownloadAgentClosedTicketsBySubCaseTypeExcel() {
        const params: ChatPerformanceParams = { startDate, endDate };
        await dashboardService.downloadAgentClosedTicketsBySubCaseTypeExcel(params, (key: string) => t(key));
    }

    async function handleDownloadAgentClosedTicketsByCaseAndSubCaseTypeExcel() {
        const params: ChatPerformanceParams = { startDate, endDate };
        await dashboardService.downloadAgentClosedTicketsByCaseAndSubCaseTypeExcel(params, (key: string) => t(key));
    }
     
    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        isInitializing = true;
        
        // Set initial values to track changes
        previousStartDate = startDate;
        previousEndDate = endDate;
        previousLanguage = $currentLanguage;
        
        fetchData().finally(() => {
            hasInitialized = true;
            isInitializing = false;
        });
    });

    // Only trigger fetchData when values actually change after initialization
    $: if (hasInitialized && !isInitializing) {
        const currentLanguage = $currentLanguage;
        
        // Check if any values have actually changed
        const hasStartDateChanged = startDate !== previousStartDate;
        const hasEndDateChanged = endDate !== previousEndDate;
        const hasLanguageChanged = currentLanguage !== previousLanguage;
        
        if (hasStartDateChanged || hasEndDateChanged || hasLanguageChanged) {
            console.log("Filter values changed, triggering fetchData:", {
                startDate: { old: previousStartDate, new: startDate },
                endDate: { old: previousEndDate, new: endDate },
                language: { old: previousLanguage, new: currentLanguage }
            });
            
            // Update tracking variables
            previousStartDate = startDate;
            previousEndDate = endDate;
            previousLanguage = currentLanguage;
            
            fetchData();
        }
    }

</script>

{#if isTicketStatusChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentTicketsByStatus')}</h3>
                <button on:click={() => isTicketStatusChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType} chartType="horizontalBar"
                        labelKey="caseType" valueKey="count" label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isSubCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h3>
                <button on:click={() => isSubCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseSubCaseTableExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h3>
                <button on:click={() => isCaseSubCaseTableExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-2">
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalIncomingMessages')}
                    value={chatVolume}
                    valueColor="text-black-600"
                    trendValue={chatVolumeTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentTickets')}
                    value={allTickets}
                    valueColor="text-black-600"
                    trendValue={allTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentClosedTickets')}
                    value={allClosedTickets}
                    valueColor="text-black-600"
                    trendValue={allClosedTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentTicketClosureRateVsIncoming')}
                    value={closedRate}
                    valueColor="text-black-600"
                    trendValue={closedRateTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentResponseTimeSeconds')}
                    value={avgResponseTime}
                    valueColor="text-black-600"
                    trendValue={responseTimeTrend} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentResponseRateWithin6Seconds')}
                    value={responseRateWithin6s}
                    valueColor="text-black-600"
                    trendValue={responseRateWithin6sTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentHandlingTimeMinutes')}
                    value={avgHandlingTime}
                    valueColor="text-black-600"
                    trendValue={handlingTimeRate} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentHandlingRateWithin5Minutes')}
                    value={handlingRateWithin5mins}
                    valueColor="text-black-600"
                    trendValue={handlingRateWithin5minsTrend} trendUnit="%"
                />
            </div>
        </div>
        <div class="lg:col-span-1 bg-white p-5 rounded-lg shadow-md">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentTicketsByStatus')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isTicketStatusChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button
                        on:click={handleDownloadAgentTicketsByStatusExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[24rem] sm:h-[24rem] lg:h-[24rem]">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isCaseTypeChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button
                        on:click={handleDownloadAgentClosedTicketsByCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType}
                        chartType="horizontalBar"
                        labelKey="caseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isSubCaseTypeChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button
                        on:click={handleDownloadAgentClosedTicketsBySubCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isCaseSubCaseTableExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button
                        on:click={handleDownloadAgentClosedTicketsByCaseAndSubCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="overflow-y-auto max-h-96">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
</div>