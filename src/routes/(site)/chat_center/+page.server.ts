import { services } from "$src/lib/api/features";
import type { PageServerLoad, Actions } from "./$types";
import { getBackendUrl } from '$src/lib/config';
import { redirect, error, fail } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ fetch, url, cookies }) => {
    try {
        // Check for access token in cookies
        let access_token = cookies.get('access_token');
        let refresh_token = cookies.get('refresh_token');
        if (!access_token) {
            throw error(401, 'No access token available');
        }

        for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
            try {
                // Get query parameters
                const page = url.searchParams.get('page') || '1';
                const search = url.searchParams.get('search') || '';
                const platform = url.searchParams.get('platform') || '';

                // Fetch all platform identities instead of customers
                const apiUrl = new URL(`${getBackendUrl()}/customer/api/platform-identities/`, url.origin);
                apiUrl.searchParams.set('page', page);
                if (search) apiUrl.searchParams.set('search', search);
                if (platform) apiUrl.searchParams.set('platform', platform);

                const response = await fetch(apiUrl.toString(),
                    {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${access_token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                if (!response.ok) {
                    throw error(response.status, 'Failed to load platform identities');
                }

                const data = await response.json();

                const all_Users = await services.users.getAll(access_token);
                const all_Statuses = await services.statuses.getAll(access_token);
                const all_Priorities = await services.ticket_priority.getAll(access_token);
                const all_ticket_topics = await services.tickets.getAllTicketTopics(access_token);
                const current_user = await services.users.getUserInfo(access_token);

                return {
                    platformIdentities: data.results || [],
                    total: data.count || 0,
                    page: parseInt(page),
                    hasMore: !!data.next,
                    access_token: access_token,
                    allUsers: all_Users.users || [],
                    allStatuses: all_Statuses.statuses || [],
                    allPriorities: all_Priorities.priorities || [],
                    allTopics: all_ticket_topics.ticket_topics || [],
                    currentLoginUser: current_user.users || [],
                };
            }
            catch (err) {
                const refreshResponse = await services.users.refreshToken(refresh_token);
                const login_token = refreshResponse.login_token;

                // If refresh token is invalid, redirect to login
                if (login_token.length === 0) {
                    cookies.set("isLogin", 'false', { path: '/' })
                    throw redirect(302, '/login');
                // If refresh token is valid, update access token and refresh token
                } else {
                    // Update access token and refresh token
                    access_token = login_token.access;
                    refresh_token = login_token.refresh;

                    // Update cookies
                    cookies.set("access_token", access_token, { path: '/' });
                    cookies.set("refresh_token", refresh_token, { path: '/' })
                }
            }
        }

    } catch (err) {
        console.error('Error loading platform identities:', err);
        throw error(500, 'Failed to load platform identities');
    }
};


function formatFieldValue(value: FormDataEntryValue | null): string | null {
    if (value === null || value === undefined) return null;
    const stringValue = value.toString().trim();
    return stringValue === '' ? null : stringValue;
}


export const actions: Actions = {
    update_customer: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const customerId = formData.get('customer_id');
        const customerData = {
            // Original fields
            name: formatFieldValue(formData.get('name')),
            date_of_birth: formatFieldValue(formData.get('date_of_birth')),
            email: formatFieldValue(formData.get('email')),
            phone: formatFieldValue(formData.get('phone')),

            // New personal information fields
            first_name: formatFieldValue(formData.get('first_name')),
            middle_name: formatFieldValue(formData.get('middle_name')),
            last_name: formatFieldValue(formData.get('last_name')),
            nickname: formatFieldValue(formData.get('nickname')),
            // gender: parseInt(formData.get('gender') as string) || 1,
            gender_id: parseInt(formData.get('gender_id') as string),

            // New identity and contact fields
            nationality: formatFieldValue(formData.get('nationality')),
            national_id: formatFieldValue(formData.get('national_id')),
            passport_number: formatFieldValue(formData.get('passport_number')),
            career: formatFieldValue(formData.get('career')),
            preferred_language: formatFieldValue(formData.get('preferred_language')) || 'Thai', // Default to 'Thai' if not provided
            preferred_contact_method: formatFieldValue(formData.get('preferred_contact_method')),

            // Address information (nested object)
            address: {
                address_line1: formatFieldValue(formData.get('address_line1')),
                address_line2: formatFieldValue(formData.get('address_line2')),
                city: formatFieldValue(formData.get('city')),
                state_province_region: formatFieldValue(formData.get('state_province_region')),
                zip_code: formatFieldValue(formData.get('zip_code')),
                country: formatFieldValue(formData.get('country')),
            }
        };

        // console.log('customerData:', customerData);

        const response = await services.customers.putById(customerId, customerData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    upload_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();

        // console.log(formData)

        let access_token = cookies.get('access_token');

        const content = formData.get("content");
        const customerId = formData.get("customer_id");
        const bodyData = { content };

        try {

            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Upload Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    update_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = encodeURIComponent(formData.get("customerId"));
        const customerNoteId = encodeURIComponent(formData.get("customerNoteId"));
        const bodyData = { content };

        try {

            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${customerNoteId}/`;

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Update Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    delete_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const customerId = encodeURIComponent(formData.get("customerId"));
        const deleteNoteId = encodeURIComponent(formData.get("deleteNoteId"));

        console.log(`customerId-monitoring - ${customerId}`);
        console.log(`deleteNoteId-monitoring - ${deleteNoteId}`);

        try {

            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${deleteNoteId}/`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Delete Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    assign_customer_tag: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const customerId = formData.get('customer_id');

        // Get all tag IDs from the form data
        const tagIds = formData.get('tag_ids[]');
        const tagIds_num = tagIds.toString()
            .split(',')
            .map(num => parseInt(num.trim()))
            .filter(num => !isNaN(num));

        const tagData = {
            tag_ids: tagIds_num,
        };

        const response = await services.customers.assignTagsById(customerId, tagData, access_token);

        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    ticket_priority_change: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        const api_key = cookies.get('api_key'); // Assuming the token is stored in cookies
        const ticket_id = formData.get('ticket_id');
        const priority_id = formData.get('new_priority_id');


        const url = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/priority/`;

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json',
                    'X-API-Key': api_key
                },
                body: JSON.stringify({
                    "priority_id": priority_id
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }

            return { success: true };
        } catch (error) {
            console.error('Change Ticket Priority error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    ticket_transfer_owner: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_owner_id = formData.get('new_owner_id');


        const url = `${getBackendUrl()}/ticket/ticket_transfer_owner/${ticket_id}/`

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_owner_id": new_owner_id
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            return { success: true };
        } catch (error) {
            console.error('Transfer Ticket Owner error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    change_ticket_status: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_status_id = Number(formData.get('new_status_id')); // Convert to number
        const new_ticket_topic = formData.get('new_ticket_topic');

        // When it close
        if (new_status_id === 6) { 
            // formData.new_ticket_topic = Number(new_ticket_topic)
            // console.log(new_status_id)
            // console.log(new_ticket_topic)
            // console.log(formData.new_ticket_topic)
            // console.log(typeof new_ticket_topic)

            // console.log(typeof formData.new_ticket_topic)
            const new_ticket_topic_array = new_ticket_topic.split(',').map(topic => Number(topic));
            // console.log(typeof new_ticket_topic_array)
            // console.log(new_ticket_topic_array)

            const url_ticket_topic = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/topics/`
            try {
                const reponse_ticket_topic = await fetch(url_ticket_topic, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${access_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "topic_ids": new_ticket_topic_array
                    })
                });
                if (!reponse_ticket_topic.ok) {
                    const errorData = await reponse_ticket_topic.json();
                    // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                    throw new Error(`Status: ${errorData.message} (${reponse_ticket_topic.status})`);
                }
            } catch (error) {
                console.error('Change Ticket Topic error:', error);
                return fail(500, { error: `${error.message}` });
            }
        }


        const url = `${getBackendUrl()}/ticket/ticket_change_status/${ticket_id}/`
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_status_id": new_status_id
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            return { success: true };
        } catch (error) {
            console.error('Change Ticket Status error:', error);
            return fail(500, { error: `${error.message}` });
        }
    }
}
