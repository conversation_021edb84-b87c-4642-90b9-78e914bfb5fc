<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { FaqService } from '$lib/api/features/llm_rag_doc/faq.service';
    import type { FaqCategory } from '$lib/api/types/faq';

    import { writable, get } from 'svelte/store';
    import { Button, Accordion, AccordionItem, DropdownItem, Dropdown, Alert, Spinner } from 'flowbite-svelte';
    import { MessageDotsSolid, GlobeSolid, ChevronDownOutline, InfoCircleSolid, ArchiveSolid, CaretRightSolid } from 'flowbite-svelte-icons';
    import type { PageData } from './$types';
    export let data: PageData;  
    const { PRIVATE_LLM_INFORMATION_URL } = data;
    
    // Initialize FAQ service
    const faqService = new FaqService(PRIVATE_LLM_INFORMATION_URL);

    // Set default selectedOption to 'All-Features'
    let selectedOption = writable<FaqCategory>("All-Features");
    
    // FAQ sub-options (accordion items)
    const faqSubOptions: FaqCategory[] = [
        "All-Features",
        "Customer Support",
        "Product Search",
        "Promotion"
    ];

    const options = [
      "FAQ",
      "Customer Support",
      "Product Search",
      "Promotion",
      "Recommendation",
    ];
    
    let chatResponse = '';
    let referenceResponse = '';
    let isLoading = false;
  
    // Function to change the selected option
    const selectOption = (option: FaqCategory) => {
      selectedOption.set(option);
    };
  
    let userInput = '';

    // Send message to chatbot using the FAQ service
    const sendMessageToChatbot = async () => {
      try {
        isLoading = true;
        
        const currentOption = get(selectedOption);
        const response = await faqService.sendMessage(userInput, currentOption);

        console.log(response);

        chatResponse = response.output.result || 'No response'; 
        referenceResponse = response.output.reference;

        // Convert newlines to <br> for proper display
        chatResponse = chatResponse.replace(/\n/g, "<br>");
        
      } catch (error) {
        console.error('Error with API request:', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        chatResponse = 'Error with API request: ' + errorMessage;
        referenceResponse = [];
      } finally {
        isLoading = false;
      }
    };

    const handleQuestionSelect = (question: string) => {
        userInput = question;
        selectedQuestion = question;
        showDropdown = false;  // Close dropdown after selection
    };

    const sample_questions = [
        'มีโปรโมชั่นอะไรแนะนำบ้างช่วงนี้', 
        'ขอดูเอกสารประกันอุบัติเหตุหน่อย', 
        'เงื่อนไขของประกันมะเร็งมีอะไรบ้าง', 
        'อาชีพแต่ละชั้นมีความหมายว่าอะไร'
    ];

    let selectedQuestion = '';
    let showDropdown = false;

    function toggleDropdown() {
        showDropdown = !showDropdown;
    }

    // Set default option on component initialization
    selectOption('All-Features');

    // Function to clear input
    const clearInput = () => {
        userInput = '';
        chatResponse = '';
        referenceResponse = '';
    };

    // import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';

</script>
  
<svelte:head>
    <title>{t('testing')}</title>
</svelte:head>

<!-- Main Container -->
<div class="flex w-full h-screen bg-gray-50">
    <!-- Left Section (Options) -->
    <div class="w-1/3 p-4 bg-white shadow-md">

        <!-- <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
              <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <span class="text-gray-400">{t('knowledgeBase')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <span class="text-gray-700">{t('testing')}</span>
            </BreadcrumbItem>
        </Breadcrumb> -->

        <h2 class="text-2xl font-semibold mb-4 flex gap-2 items-center justify-start">
            <GlobeSolid class="mt-0.5"/>
            {t('service_endpoints')}
        </h2>

        <!-- FAQ Accordion -->
        <Accordion flush>
            <AccordionItem label="FAQ" open>
                <span slot="header">{t('faq_service')}</span>
                {#each faqSubOptions as option}
                    <Button 
                        on:click={() => selectOption(option)} 
                        color={option === $selectedOption ? 'blue' : 'gray'}
                        class="w-full text-left inline-block"
                    >
                        {option}
                    </Button>
                {/each}

                <Alert color="dark">
                    <InfoCircleSolid slot="icon" class="w-5 h-5" />
                    <span class="font-medium">{t('note')}:</span>
                    {t('note_faq')}
                </Alert>
            </AccordionItem>
        </Accordion>
      
        <div>
            <Button color="dark">{t('example_message')}<ChevronDownOutline class="w-6 h-6 ms-2 text-white dark:text-white" /></Button>
            <Dropdown>
                {#each sample_questions as question, index}
                    <DropdownItem on:click={() => handleQuestionSelect(question)} key={index}>
                    {question}
                    </DropdownItem>
                {/each}
            </Dropdown>
        </div>
    </div>
  
    <!-- Right Section (Chatbot) -->
    <div class="w-2/3 p-6 bg-gray-100 flex flex-col">
        <div class="mb-6">
            <h2 class="text-2xl font-bold">{t('testing_section_title')}</h2>
            <p class="text-gray-600">{t('testing_section_description')}</p>
        </div>
        
        <!-- Header with selected service -->
        <h2 class="text-lg font-semibold text-gray-700 mb-4">
            Testing - 
            {#if $selectedOption === 'All-Features'}
                FAQ
            {:else if $selectedOption === 'Customer Support'}
                Customer Support
            {:else if $selectedOption === 'Product Search'}
                Product Search
            {:else if $selectedOption === 'Promotion'}
                Promotion
            {:else if $selectedOption === 'Recommendation'}
                Recommendation
            {:else}
                Chatbot
            {/if}
        </h2>
        
       <!-- Chat Container -->
        <div class="bg-white rounded-lg shadow">
            <!-- User Input -->
            <div class="flex items-center gap-2 p-4 border-b border-gray-100">
                <div class="relative w-full">
                    <input 
                        type="text" 
                        bind:value={userInput} 
                        class="w-full rounded-full border border-gray-300 px-4 py-2 pr-10"
                        placeholder={t('type_message_here')}
                        on:keypress={(event) => {
                            if (event.key === 'Enter') {
                                sendMessageToChatbot();
                            }
                        }}
                    />
                    <!-- Clear button - only show when there's input -->
                    {#if userInput}
                        <button 
                            on:click={clearInput}
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    {/if}
                </div>
                
                <Button 
                    on:click={sendMessageToChatbot} 
                    class="ml-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!userInput || isLoading} 
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {#if isLoading}
                            
                            <Spinner class="me-3" size="4" color="white" />
                        {:else}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            {/if}
                    </svg>
                </Button>
            </div>

            <!-- Chatbox -->
            <div class="p-6"> 
                <h3 class="font-semibold text-gray-700 flex items-center">
                    <MessageDotsSolid class="w-5 h-5 mr-2 text-blue-500" />
                    {t('chatbot_response')}:
                </h3>
                {#if isLoading}
                    <div class="d-flex align-items-center">
                        <Spinner color='blue' class="me-3" size="8" />
                        <span>{t('loading')}...</span>
                    </div>
                {:else}
                    <p class="text-gray-700 py-3" style="white-space: pre-wrap">{@html chatResponse}</p>
                {/if}
            </div>
        </div>

        <!-- Reference Section -->
        <div class="mt-4 bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-2">
                <h3 class="font-semibold text-gray-700 flex items-center">
                    <InfoCircleSolid class="w-5 h-5 mr-2 text-blue-500" />
                    {t('reference_information')}
                </h3>
            </div>
            <div class="space-y-3 max-h-64 overflow-y-auto">
                {#if !isLoading && chatResponse !== 'No response' && chatResponse !== '' && referenceResponse && referenceResponse.length > 0}
                    {#each referenceResponse as ref, index}
                        <Accordion flush>
                            <AccordionItem>
                                <span slot="header" class="font-medium text-blue-600">
                                    <div class="flex items-center">
                                        <CaretRightSolid class="w-4 h-4 mr-2" />
                                        {ref.metadata.question}
                                    </div>
                                </span>
                                <div class="p-2 bg-gray-50 rounded">
                                    <p class="text-gray-700">{ref.metadata.answer}</p>
                                </div>
                            </AccordionItem>
                        </Accordion>
                    {/each}
                {/if}
            </div>
        </div>
    </div>
</div>