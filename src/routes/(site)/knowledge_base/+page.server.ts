import type { PageServerLoad } from "./$types";
import type { Actions } from '@sveltejs/kit';
import { fail, error, redirect } from '@sveltejs/kit';
import { getBackendUrl } from '$src/lib/config';

import { services } from "$lib/api/features";


export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    let user_role = cookies.get('user_role');
    // let user_role = "Supervisor";
    if (!access_token) {
        return {
            documents: [],
            error: 'No access token available'
        };
    }


    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const response = await services.documents.getAll(access_token);

            if (response.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            // Group documents by category
            // const categorizedDocuments = {
            //     "CUSTOMER_SUPPORT": [],
            //     "PROMOTION": [],
            //     "PRODUCT": []
            // };

            // (response.documents || []).forEach((doc: DocumentInterface) => {
            //     if (doc.category in categorizedDocuments) {
            //         categorizedDocuments[doc.category].push(doc);
            //     }
            // });

            const customerSupportRes = await services.documents.getCustomerSupportDocs(access_token);
            const promotionRes = await services.documents.getPromotionDocs(access_token);
            const productDocsRes = await services.documents.getProductDocs(access_token);

            if (customerSupportRes.res_status === 401 || promotionRes.res_status === 401 || productDocsRes.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            }

            let productDocs = [];
            productDocsRes.documents.results.forEach(productDoc => {
                const excel_document = productDoc['excel_document'];
                excel_document['image_document'] = productDoc['image_document'];
                excel_document['product_type'] = productDoc['products'] ? productDoc['products'][0]['product_type'] : "",
                    productDocs.push(excel_document);
            });

            const categorizedDocuments = {
                "CUSTOMER_SUPPORT": customerSupportRes.documents.results,
                "PROMOTION": promotionRes.documents.results,
                "PRODUCT": productDocs,

            };

            return {
                documents: categorizedDocuments || [], // Ensure we always return an array,
                access_token: access_token,
                user_role: user_role
            };
        } catch (err) {
            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
}


export const actions: Actions = {
    // TODO - Delete this when the next version is work
    // This version is work with upload file to local storage on the Backend
    // upload_file: async ({ request, fetch, cookies }) => {
    //     const formData = await request.formData();
    //     // let collection_name = formData.get('collection_name');
    //     let access_token = cookies.get('access_token')
    //     let collection_name = ""

    //     // TODO - Clean this ?
    //     const print_data_json = {
    //         uploaded_file: formData.get("uploaded_file"),
    //         image_file: formData.get("image_file"),
    //         // topic: formData.get("topic"),
    //         category: formData.get("category"),
    //         content: formData.get("content"),
    //         // llm_id: formData.get("llm_id"),
    //         // is_active: true,
    //         // TODO - Change to input' value from the page
    //         // is_active: formData.get("is_active"),
    //     }

    //     // Set values according to given inputs
    //     if ( formData.get("category") == "PRODUCT" ) {
    //         formData.set("topic", "RECOMMENDATION");
    //         formData.set("llm_id", "4");
    //         formData.set("is_active", "true");
    //         collection_name = formData.get("category").toLowerCase()
    //     } else if ( formData.get("category") == "TEMPLATE" ) {
    //         formData.set("topic", "TEMPLATE");
    //         formData.set("llm_id", "2"); // Default LLM
    //         formData.set("is_active", "true");
    //         collection_name = formData.get("category").toLowerCase()
    //     } else {
    //         formData.set("topic", "FAQ");
    //         formData.set("llm_id", "3");
    //         formData.set("is_active", "true");
    //         collection_name = formData.get("category").toLowerCase()
    //     }

    //     try {

    //         // TODO - Clean this
    //         // console.log(`Knowledge page's page.server.ts, upload_file, collection_name - ${collection_name}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, uploaded_file - ${formData.get("uploaded_file")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, image_file - ${formData.get("image_file")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, topic - ${formData.get("topic")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, category - ${formData.get("category")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, collection_name - ${formData.get("category").toLowerCase()}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, content - ${formData.get("content")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, llm_id - ${formData.get("llm_id")}`)
    //         // console.log(`Knowledge page's page.server.ts, upload_file, is_active - ${formData.get("is_active")}`)

    //         // const url = `${getBackendUrl()}/llm_rag_doc/api/document`

    //         // console.log(`Knowledge page's page.server.ts, upload_file, url - ${url}`)


    //         // const response = await fetch(url, {
    //         //     method: 'GET',
    //         //     headers: {
    //         //         'Authorization': `Bearer ${access_token}`
    //         //     },
    //         // });

    //         const url = `${getBackendUrl()}/llm_rag_doc/upload_document/?collection_name=${collection_name}`

    //         // TODO - Delete this
    //         // console.log(`Knowledge page's page.server.ts, upload_file, url - ${url}`)


    //         const response = await fetch(url, {
    //             method: 'POST',
    //             headers: {
    //                 'Authorization': `Bearer ${access_token}`
    //             },
    //             body: formData
    //         });

    //         if (!response.ok) {
    //             throw new Error(`HTTP error! status: ${response.status}`);
    //         }

    //         const result = await response.json();
    //         return { success: true };
    //     } catch (error) {
    //         console.error('Upload error:', error);
    //         return fail(500, { error: 'File upload failed' });
    //     }
    // },

    upload_file: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        // let collection_name = formData.get('collection_name');
        let access_token = cookies.get('access_token');

        // TODO - Clean this ?
        const print_data_json = {
            file: formData.get("file"),
            image_file: formData.get("image_file"),
            category: formData.get("category"),
            description: formData.get("description"),
            start_date: formData.get("start_date"),
            end_date: formData.get("end_date"),
            product_type: formData.get("selectedProductType"),
            // TODO - Backend should get list of selectedChannel ?
            // company: formData.get("selectedChannel"),
            // company: Array.from(formData.get('selectedChannel')),
            // company: JSON.parse(formData.get("selectedChannel") as string)
        }

        if (formData.get("file").size === 0) {
            return fail(400, { error: 'File is not uploaded.' });
        }

        function normalizeDate(dateString) {
            return dateString.split('T')[0]; // Extract only the date part (YYYY-MM-DD)
        }

        // server-side form validation
        if (formData.get("category") === "PROMOTION") {
            let start_date = formData.get("start_date");
            let end_date = formData.get("end_date");

            if (start_date.length === 0 || end_date.length === 0) {
                return fail(400, { error: 'Start date and end date must be provided.' });
            }

            let normalizedStartDate = normalizeDate(start_date);
            let normalizedEndDate = normalizeDate(end_date);

            console.log("This hit");
            

            if (normalizedEndDate < normalizedStartDate) {
                return fail(400, { error: 'End date must be equal to or greater than start date.' });
            }
        } else if (formData.get("category") === "PRODUCT") {
            if (formData.get("image_file").size === 0) {
                return fail(400, { error: 'Image file is not uploaded.' });
            }

            if (formData.get("selectedProductType").length  === 0) {
                return fail(400, { error: 'Product type is required.' });
            }
        }


        // Set values according to given inputs
        if (formData.get("category") == "PRODUCT") {
            formData.set("topic", "RECOMMENDATION");
            formData.set("is_active", "true");
            // collection_name = formData.get("category").toLowerCase()
        } else if (formData.get("category") == "TEMPLATE") {
            formData.set("topic", "TEMPLATE");
            formData.set("is_active", "true");
            // collection_name = formData.get("category").toLowerCase()
        } else {
            formData.set("topic", "FAQ");
            formData.set("is_active", "true");
            // collection_name = formData.get("category").toLowerCase()
        }

        try {
            // TODO - Clean this            
            console.log(`Knowledge page's page.server.ts, upload_file action, print_data_json - ${JSON.stringify(print_data_json)}`)
            // console.log('Selected Channel Array:', Array.from(formData.get('selectedChannel')));
            // console.log('Selected Channel Values:', [...formData.get('selectedChannel')]);
            // console.log(`Knowledge page's page.server.ts, upload_file action, company - ${print_data_json['company']}`)
            // console.log(`Knowledge page's page.server.ts, upload_file action, company - ${JSON.stringify(print_data_json['company'])}`)

            const url = `${getBackendUrl()}/llm_rag_doc/azure/blob/files/upload/`

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`
                },
                body: formData
            });

            if (!response.ok) {
                // Parse the response JSON to extract the error message
                const response_json = await response.json();
                const errorMessage = response_json.error || 'Unknown error occurred';
                return fail(response.status, { error: errorMessage });
            }
    
            const result = await response.json();
            return { success: true };
        } catch (error) {
            console.error('Upload error:', error);
            // In case of network issues or other exceptions,
            // return the error message from the caught error
            return fail(500, { error: error.message || 'Unknown error occurred.' });
        }
    },

    edit_document: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();

        const document = Object.fromEntries(formData)
        const access_token = cookies.get('access_token');

        const url = `${getBackendUrl()}/llm_rag_doc/api/document/${formData.get('id')}/`

        try {

            // TODO - Clean this
            // formData.set()
            console.log(`Knowledge page's page.server.ts, edit_document, url - ${url}`)
            console.log(`Knowledge page's page.server.ts, edit_document, formData - ${formData}`)
            console.log(`Knowledge page's page.server.ts, edit_document, document - ${JSON.stringify(document)}`)

            const response = await fetch(url, {

                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`
                },
                body: formData
            });

            if (!response.ok) {
                // throw new Error(`HTTP error! status: ${response.status}`);
                throw new Error(`Status: ${response.status}`);
            }

            return { success: true };
        } catch (error) {

            console.error('Edit error:', error);
            return fail(500, { error: 'Failed to edit document' });
        }
    },

    delete_document: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const documentIdsRaw = formData.get('document_ids');

        if (!documentIdsRaw) {
            return fail(400, { error: 'document_ids is required' });
        }

        const document_ids = documentIdsRaw.split(",").map(Number);

        if (document_ids.some(isNaN)) {
            return fail(400, { error: 'Invalid document_ids format' });
        }

        console.log(document_ids);

        const bodyData = { document_ids };
        const access_token = cookies.get('access_token');

        const url = `${getBackendUrl()}/llm_rag_doc/azure/blob/files/batch-delete/`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json' // Ensure JSON request
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorMessage = await response.text();
                // throw new Error(`HTTP error! status: ${response.status}, message: ${errorMessage}`);
                throw new Error(`Status: ${errorMessage} (${response.status})`);
            }

            return { success: true };
        } catch (error) {
            console.error('Delete error:', error);
            return fail(500, { error: 'Failed to delete document' });
        }


    }

    // delete_document: async ({ request, fetch, cookies }) => {
    //     const formData = await request.formData();
    //     const id = formData.get('id') as string;
    //     const access_token = cookies.get('access_token');
    //     let filename = formData.get('filename');
    //     // let collection_name = formData.get('collection_name');

    //     // Set values according to given inputs
    //     // if ( formData.get("category") == "product" ) {
    //     //     formData.set("collection_name", "RECOMMENDATION");
    //     // } else {
    //     //     formData.set("collection_name", "true");
    //     // }

    //     let collection_name = formData.get('category').toLowerCase();

    //     // const url = `${getBackendUrl()}/llm_rag_doc/delete_document/?filename=${filename}&collection_name=${collection_name}`
    //     // const url = `${getBackendUrl()}/llm_rag_doc/delete_document/?filename=${filename}&collection_name=${collection_name}`

    //     // TODO - Delete this
    //     console.log(`delete_document's ${getBackendUrl()}`)

    //     const url = `${getBackendUrl()}/llm_rag_doc/azure/blob/files/delete/${id}/`

    //     // TODO - Clean this
    //     console.log(`Knowledge page's page.server.ts, delete_document, id - ${id}`)
    //     console.log(`Knowledge page's page.server.ts, delete_document, filename - ${filename}`)
    //     console.log(`Knowledge page's page.server.ts, delete_document, category - ${formData.get('category')}`)
    //     console.log(`Knowledge page's page.server.ts, delete_document, collection_name - ${collection_name}`)

    //     try {
    //         const response = await fetch(url, {
    //             method: 'DELETE',
    //             headers: {
    //                 'Authorization': `Bearer ${access_token}`
    //             }
    //         });

    //         if (!response.ok) {
    //             throw new Error(`HTTP error! status: ${response.status}`);
    //         }

    //         return { success: true };
    //     } catch (error) {
    //         console.error('Delete error:', error);
    //         return fail(500, { error: 'Failed to delete document' });
    //     }
    // },
};