<script lang="ts">
  	import type { PageData } from './$types';
    import { t } from '$src/lib/stores/i18n';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { AccordionItem, Accordion } from "flowbite-svelte";
    import { ArchiveSolid, FileLinesSolid, UserHeadsetSolid, BullhornSolid, FileImageSolid, TrashBinSolid, FolderOpenSolid } from 'flowbite-svelte-icons';

    import UploadDocuments from '$src/lib/components/knowledge/RightSection/UploadDocuments.svelte';
    import DisplayDocument from '$src/lib/components/knowledge/RightSection/DisplayDocument.svelte';
    import APIConnection from '$src/lib/components/knowledge/APIConnection.svelte';

    import { enhance } from '$app/forms';
    import {
      Heading,
      P,
      Card,
      Select,
      Label,
      Fileupload,
      Textarea,
      Datepicker,
      Button,
      Spinner,
      Toast
    } from 'flowbite-svelte';

    let categorySelect = '';
    let categories = [
      { value: 'CUSTOMER_SUPPORT', name: 'Customer Support' },
      { value: 'PROMOTION', name: 'Promotion' },
      { value: 'PRODUCT', name: 'Product' }
    ];

    let productTypeSelect = '';
    let productTypes = [
      { value: 1, name: 'CAR' },
      { value: 2, name: 'COMPULSORY_MOTOR' },
      { value: 3, name: 'HEALTH_ACCIDENT_TRAVEL' },
      { value: 4, name: 'BUSINESS' },
      { value: 5, name: 'HOME' },
      { value: 6, name: 'SHIPPING' },
      { value: 7, name: 'CANCER' },
      { value: 8, name: 'CYBER' }
    ];

    let activeTab = 'documents'; // Default active tab

    function setActiveTab(tab: string) {
        activeTab = tab;
    }

    export let documents = [];
    export let user_role = "Agent";
    export let error = '';

    // Count the number of documents for a specific category
    $:  countByCategory = (category: string, active: boolean) => {
      return documents[category]
        ? documents[category].filter((doc) => doc.is_active === active).length
        : 0;
    };

    export let data: PageData;
    $: ({ documents, error, access_token, user_role } = data);

    $: allDocuments = [
      ...(documents['CUSTOMER_SUPPORT'] || []),
      ...(documents['PROMOTION'] || []),
      ...(documents['PRODUCT'] || [])
    ];

    
    // API Integration State
  let searchTerm = '';
  let selectedCategory = 'all';
  let filterCategory = '';
  let filterStatus = '';
  let filterDate = '';

  // API Categories
  let apiCategories = [
    { value: 'CRM', name: 'CRM' },
    { value: 'Databases', name: 'Databases' }
  ];

  // Available APIs for connection (shown in cards)
  let availableApis = [
    {
      id: 'salesforce',
      name: 'Salesforce',
      description: 'Cloud-based customer relationship management (CRM) platform that helps businesses manage sales...',
      category: 'CRM',
      color: 'blue',
      iconName: 'salesforce'
    },
    {
      id: 'mongodb',
      name: 'MongoDB',
      description: 'MongoDB is an open source NoSQL database management program.',
      category: 'Databases',
      color: 'blue',
      iconName: 'mongodb'
    },
    {
      id: 'mysql',
      name: 'MySQL',
      description: 'MySQL is an open-source relational database management system.',
      category: 'Databases',
      color: 'blue',
      iconName: 'mysql'
    },
    {
      id: 'postgresql',
      name: 'PostgreSQL',
      description: 'PostgreSQL is a free and open-source relational database management system emphasizing extensibility...',
      category: 'Databases',
      color: 'blue',
      iconName: 'postgresql'
    },
    {
      id: 'azure_sql',
      name: 'Microsoft Azure SQL Database',
      description: 'Build apps that scale with managed and intelligent SQL database in the cloud.',
      category: 'Databases',
      color: 'blue',
      iconName: 'azure-sql'
    }
  ];

  // Connected APIs (shown in table) - can have multiple connections of same type
  let connectedApis = [
    {
      id: 1,
      name: 'Customer Database API',
      category: 'API',
      connected_date: '10/1/2567',
      permissions: t('admin_supervisor'),
      status: t('connected'),
      iconName: 'api'
    },
    {
      id: 2,
      name: 'Policy Management API',
      category: 'API',
      connected_date: '12/1/2567',
      permissions: t('admin_only'),
      status: t('connected'),
      iconName: 'api'
    },
    {
      id: 3,
      name: 'Claims Processing API',
      category: 'API',
      connected_date: '14/1/2567',
      permissions: t('admin_supervisor_staff'),
      status: t('connected'),
      iconName: 'api'
    },
    {
      id: 4,
      name: 'MySQL Production DB',
      category: 'Database',
      connected_date: '15/1/2567',
      permissions: t('admin_only'),
      status: t('connected'),
      iconName: 'mysql'
    },
    {
      id: 5,
      name: 'MySQL Staging DB',
      category: 'Database',
      connected_date: '16/1/2567',
      permissions: t('admin_developer'),
      status: t('connected'),
      iconName: 'mysql'
    }
  ];

  // Filtered APIs for cards
  $: filteredApis = availableApis.filter(api => {
    const matchesSearch = searchTerm === '' || 
      api.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      api.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || api.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Filtered connections for table
  $: filteredConnections = connectedApis.filter(connection => {
    const matchesCategory = filterCategory === '' || connection.category === filterCategory;
    const matchesStatus = filterStatus === '' || connection.status === filterStatus;
    // Add date filtering logic here if needed
    
    return matchesCategory && matchesStatus;
  });

  // Search handler
  function handleSearch(event) {
    searchTerm = event.target.value;
  }

  // Category filter
  function filterByCategory(category) {
    selectedCategory = category;
  }

  // Delete connection
  function deleteConnection(connectionId) {
    connectedApis = connectedApis.filter(conn => conn.id !== connectionId);
  }

</script>

<svelte:head>
	<title>{t('knowledge_base')}</title>
</svelte:head>

<div class="min-h-screen rounded-lg bg-white">
    <div id="knowledge-base-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
      <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
          <BreadcrumbItem href="/" home>
            <span class="text-gray-400">{t('home')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <span class="text-gray-400">{t('knowledgeBase')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <span class="text-gray-700">{t('upload_files')}</span>
          </BreadcrumbItem>
      </Breadcrumb>

      <div class="mb-2">
          <h2 class="text-2xl font-bold">{t('knowledgeBase')}</h2>
          <p class="text-gray-600">{t('knowledge_base_description')}</p>
      </div>

      <!-- Tab Navigation -->
      <div class="flex p-1 bg-gray-100 border-gray-200 rounded-xl mb-1">
          <button
              class="{activeTab === 'documents' 
                  ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' 
                  : 'text-gray-500'} 
                  w-1/2 py-1 rounded-lg transition-colors"
              aria-current={activeTab === 'documents' ? 'page' : undefined}
              on:click={() => setActiveTab('documents')}
          >
              {t('documents')}
          </button>
          <button
              class="{activeTab === 'api_integration' 
                  ? 'bg-white text-gray-800 border border-gray-300 shadow-sm' 
                  : 'text-gray-500'} 
                  w-1/2 py-1 rounded-lg transition-colors"
              aria-current={activeTab === 'api_integration' ? 'page' : undefined}
              on:click={() => setActiveTab('api_integration')}
          >
              {t('api_integration')}
          </button>
      </div>

      <!-- Tab Content -->
      <div id="tab-content" class="p-1">
        {#if activeTab === 'documents'}
            <!-- Statistics knowledge base -->
            <div id='statistics-kb' class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                <!-- Total Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <FileLinesSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('total')}</p>
                        <p class="text-2xl font-bold">
                            {countByCategory('CUSTOMER_SUPPORT', true) +
                                countByCategory('PROMOTION', true) +
                                countByCategory('PRODUCT', true)}
                        </p>
                    </div>
                </div>

                <!-- Promotion Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <BullhornSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('promotion')}</p>
                        <p class="text-2xl font-bold">{countByCategory('PROMOTION', true)}</p>
                    </div>
                </div>

                <!-- Customer Support Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <UserHeadsetSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('customer_support')}</p>
                        <p class="text-2xl font-bold">{countByCategory('CUSTOMER_SUPPORT', true)}</p>
                    </div>
                </div>

                <!-- Product Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <FileImageSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('product')}</p>
                        <p class="text-2xl font-bold">{countByCategory('PRODUCT', true)}</p>
                    </div>
                </div>

                <!-- Trash Documents -->
                <div class="flex items-center rounded-lg bg-gray-100 p-4 shadow-md">
                    <div class="mr-3 rounded-full bg-gray-100 p-2">
                        <TrashBinSolid class="h-6 w-6 text-gray-700" />
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">{t('trash')}</p>
                        <p class="text-2xl font-bold">
                            {countByCategory('CUSTOMER_SUPPORT', false) +
                                countByCategory('PROMOTION', false) +
                                countByCategory('PRODUCT', false)}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Upload files -->
            <div id="upload-files-tab" class="mt-4">
                <!-- Upload Files -->
                <Accordion>
                    <AccordionItem class="hover:bg-gray-50 rounded-lg">
                        <span slot="header" class="p-2 grid grid-cols-[auto_1fr] gap-3 items-center">
                        <ArchiveSolid class="w-5 h-5" />
                        <div class="flex flex-col">
                            <div class="text-sm font-semibold">{t('upload_files')}</div>
                            <div class="text-sm text-gray-500">{t('upload_files_description')}</div>
                        </div>
                        </span>

                        <UploadDocuments 
                        documents={documents}
                        {user_role}
                        />
                    </AccordionItem>
                </Accordion>

                <!-- List of Documents -->
                <DisplayDocument
                  documents={allDocuments}
                  {access_token}
                  {user_role}
                />
            </div>
        {/if}

        {#if activeTab === 'api_integration'}
            <APIConnection />
        {/if}
      </div>
  </div>
</div>