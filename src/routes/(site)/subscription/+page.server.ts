import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { services } from '$lib/api/features';
import type {
    SubscriptionData,
    CurrentSubscription
} from '$lib/api/types/subscription-api';

export const load: PageServerLoad = async ({ cookies }) => {
	let access_token = cookies.get('access_token');

	if (!access_token) {
		return {
			subscriptionData: null,
			userRole: null,
			error: 'No access token available'
		};
	}

	// Verify user authentication and role
	const response_userInfo = await services.users.getUserInfo(access_token);
	if (response_userInfo.res_status === 401) {
		throw error(401, 'Invalid access token!!!');
	}

	// Check if user has permission to view subscription settings
	// Note: getUserInfo returns a single user object, not an array despite the interface
	const user = response_userInfo.users as any;
	const role = user.roles?.[0]?.name;
	if (role !== 'Admin' && role !== 'Supervisor') {
		throw redirect(302, '/');
	}

	try {
		// Fetch subscription data from backend API
		const [subscriptionInfoResponse, quotaStatusResponse] = await Promise.all([
			services.subscription.getSubscriptionInfo(access_token),
			services.subscription.getQuotaStatus(access_token),
		]);

		// Check if subscription is not found
		if (subscriptionInfoResponse.res_status === 404 || !subscriptionInfoResponse.data) {
			console.error('Failed to fetch subscription info:', subscriptionInfoResponse.error_msg);
			return {
				subscriptionData: null,
				userRole: role,
				error: subscriptionInfoResponse.error_msg || 'Failed to fetch subscription info'
			};
		}

		// Check if quota status request was successful
		if (quotaStatusResponse.res_status !== 200 || !quotaStatusResponse.data) {
			console.error('Failed to fetch quota status:', quotaStatusResponse.error_msg);
			return {
				subscriptionData: null,
				userRole: role,
				error: quotaStatusResponse.error_msg || 'Failed to fetch subscription quota status'
			};
		}

		// Debug
		// console.log('quotaStatusResponse.data', quotaStatusResponse.data);
		// console.log('subscriptionInfoResponse.data', subscriptionInfoResponse.data);

		// Transform backend data to frontend format
		const currentSubscription: CurrentSubscription = {
			tier: subscriptionInfoResponse.data.tier_name,
			status: subscriptionInfoResponse.data.status,
			expiresAt: subscriptionInfoResponse.data.expires_at,
			serialNumber: subscriptionInfoResponse.data?.subscription_key || 'N/A',
			organizationName: subscriptionInfoResponse.data.organization_name,
			activatedOn: subscriptionInfoResponse.data?.activated_on
		};

		const subscriptionData: SubscriptionData = {
			currentSubscription,
			quotaData: quotaStatusResponse.data,
		};

		return {
			subscriptionData,
			userRole: role,
			error: null
		};
	} catch (err) {
		console.error('Error loading subscription data:', err);
		return {
			subscriptionData: null,
			userRole: role,
			error: 'Failed to load subscription data'
		};
	}
};
