// Customer related types

export interface Customer {
    customer_id: number;
    universal_id?: string;

    // Personal Information
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    name?: string;
    nickname?: string;
    title?: string;

    // Demographics
    date_of_birth?: string;
    age?: number;
    gender_id?: number;
    nationality?: string;

    // Contact Information
    email?: string;
    email_verified?: boolean;
    email_verified_date?: string;
    phone?: string;
    phone_verified?: boolean;
    phone_verified_date?: string;

    // Address
    address_line1?: string;
    address_line2?: string;
    subdistrict?: string;
    district?: string;
    province?: string;
    zip_code?: string;
    country?: string;

    // Professional Information
    career?: string;
    occupation?: string;
    company_name?: string;
    industry?: string;
    annual_income_range?: string;

    // Preferences
    preferred_language?: string;
    preferred_contact_method?: string;
    preferred_contact_time?: string;
    accepts_marketing?: boolean;
    accepts_sms?: boolean;
    accepts_email?: boolean;
    accepts_push_notifications?: boolean;

    // Customer Relationship
    customer_type: 'PROSPECT' | 'NEW' | 'REGULAR' | 'VIP' | 'INACTIVE';
    customer_segment?: string;
    lifetime_value?: number;
    referral_source?: string;
    referred_by?: number;
    referral_code?: string;

    // Account Status
    account_status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'BLACKLISTED' | 'DELETED';
    risk_level?: string;
    kyc_status?: string;
    kyc_verified_date?: string;

    // Activity Tracking
    first_contact_date?: string;
    last_contact_date?: string;
    last_purchase_date?: string;
    total_purchases?: number;
    total_spent?: number;

    // Platform Specific
    main_interface_id?: number;
    platform_identities?: CustomerPlatformIdentity[];

    // Tags and Notes
    customer_tags?: CustomerTag[];
    tags?: CustomerTag[];
    notes?: string;
    custom_fields?: any;

    // UI/Display fields
    platforms?: PlatformBadge[];
    last_message_time?: number;
    open_tickets?: number;
    total_messages?: number;
    unread_count?: number;
    latest_ticket_status?: string;

    // Metadata
    created_by?: number;
    created_on?: string;
    updated_by?: number;
    updated_on?: string;
}

export interface CustomerPlatformIdentity {
    id: number;
    customer: number;
    platform: 'LINE' | 'WHATSAPP' | 'FACEBOOK' | 'TELEGRAM' | 'INSTAGRAM';
    platform_user_id: string;
    platform_username?: string;  // Username on the platform (if available)
    provider_id?: string;
    provider_name?: string;
    channel_id?: string;
    channel_name?: string;
    display_name?: string;
    picture_url?: string;
    status_message?: string;
    platform_data?: any;
    is_active: boolean;
    is_verified: boolean;
    last_interaction?: string;
    created_on?: string;
    unread_count?: number;
}

// export interface Message {
//     id: number;
//     ticket_id: number;
//     message: string;
//     user_name: string;
//     is_self: boolean;
//     message_type: 'TEXT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO';
//     status: 'SENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
//     created_on: string;
//     delivered_on?: string;
//     read_on?: string;
//     file_url?: string;
//     metadata?: any;
//     platform_identity?: number;
//     platform_identity_id?: number;
//     user_image_url?: string;
//     is_cross_channel?: boolean;
//     source_channel?: string;
// }

export interface MessageFile {
    name: string;
    size: number;
    type: string;
    uploaded_at: string;
    blob_path?: string;
}

export interface UploadedFileMetadata {
    file_id: string;
    original_name: string;
    url: string;
    metadata: {
        name: string;
        size: number;
        type: string;
        uploaded_at: string;
        blob_path: string;
    };
}

export interface FileUploadState {
    file: File; // Original file for preview/thumbnail
    status: 'uploading' | 'uploaded' | 'failed';
    progress?: number;
    uploadedMetadata?: UploadedFileMetadata;
    error?: string;
}

export interface FileUploadResponse {
    success: boolean;
    ticket_id: number;
    uploaded_files: UploadedFileMetadata[];
    failed_files: Array<{
        original_name: string;
        size: number;
        error: string;
    }>;
    summary: {
        total_attempted: number;
        successful: number;
        failed: number;
        total_size: number;
    };
}

export interface MessageFileMetadata {
    files: MessageFile[];
    total_files: number;
    total_size: number;
    uploaded_at: string;
}

export interface Message {
    id: number;
    ticket_id: number;
    message: string;
    user_name: string;
    is_self: boolean;
    message_type: 'TEXT' | 'IMAGE' | 'FILE' | 'TEXT_FILE' | 'AUDIO' | 'VIDEO';
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
    created_on: string;
    updated_on?: string;
    platform_identity?: number;
    created_by?: number;

    // File-related fields
    file_url?: string[];
    file_metadata?: MessageFileMetadata;
    has_attachments?: boolean;
    attachment_count?: number;
    total_file_size?: number;
    files?: MessageFile[];  // Processed from file_metadata

    // UI helpers
    user_image_url?: string;
}

export interface CustomerTag {
    id: number;
    name: string;
    description?: string;
    color?: string;
}

export interface PlatformBadge {
    platform: string;
    verified: boolean;
    last_interaction?: string;
    status?: string;
}

export interface CustomerStats {
    customer_id: number;
    total_tickets: number;
    open_tickets: number;
    closed_tickets: number;
    total_messages: number;
    avg_response_time?: string;
    last_24h_messages: number;
    last_7d_messages: number;
    last_30d_messages: number;
    customer_since?: string;
    lifetime_value?: number;
    active_platforms: string[];
}

export interface LinkingCodeResponse {
    success: boolean;
    code: string;
    expires_at: string;
    expires_in_hours: number;
}

// Policy and Claims related types

export type PolicyStatus = 'ACTIVE' | 'EXPIRED' | 'PENDING' | 'CANCELLED' | 'SUSPENDED' | 'WAITING_PERIOD';
export type PolicyType = 'LIFE' | 'HEALTH' | 'AUTO' | 'PROPERTY' | 'TRAVEL' | 'DISABILITY' | 'CRITICAL_ILLNESS';
export type ClaimStatus = 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'PAID' | 'CLOSED' | 'PENDING_DOCUMENTS';
export type ClaimType = 'DEATH' | 'MEDICAL' | 'ACCIDENT' | 'PROPERTY_DAMAGE' | 'THEFT' | 'DISABILITY' | 'CRITICAL_ILLNESS' | 'HOSPITALIZATION';

export interface PolicyProduct {
    id: number;
    name: string;
    product_type: PolicyType;
    description?: string;
    category?: string;
    provider?: string;
    coverage_details?: string[];
    exclusions?: string[];
}

export interface PolicyBeneficiary {
    id: number;
    name: string;
    relationship: string;
    percentage: number;
    contact_info?: {
        phone?: string;
        email?: string;
        address?: string;
    };
}

export interface PolicyDocument {
    id: number;
    document_type: 'POLICY_CERTIFICATE' | 'TERMS_CONDITIONS' | 'CLAIM_FORM' | 'MEDICAL_REPORT' | 'OTHER';
    file_name: string;
    file_url?: string;
    file_size?: number;
    uploaded_date: string;
    uploaded_by?: string;
}

export interface Policy {
    id: number;
    policy_number: string;
    customer_id: number;

    // Product Information
    product: PolicyProduct;

    // Policy Details
    policy_status: PolicyStatus;
    issue_date: string;
    start_date: string;
    end_date: string;
    renewal_date?: string;

    // Financial Information
    premium_amount: number;
    coverage_amount: number;
    deductible?: number;
    currency: string;
    payment_frequency: 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL';
    next_payment_date?: string;
    insurer: string;
    plan_code: string;
    plan_name: string;

    // Beneficiaries and Coverage
    beneficiaries?: PolicyBeneficiary[];
    coverage_details?: Record<string, any>;

    // Documents
    documents?: PolicyDocument[];

    // Claims Summary
    total_claims?: number;
    active_claims?: number;
    total_claims_amount?: number;

    // Metadata
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
    notes?: string;
}

export interface ClaimDocument {
    id: number;
    claim_id: number;
    document_type: 'MEDICAL_REPORT' | 'POLICE_REPORT' | 'RECEIPT' | 'PHOTO_EVIDENCE' | 'WITNESS_STATEMENT' | 'OTHER';
    file_name: string;
    file_url?: string;
    file_size?: number;
    uploaded_date: string;
    uploaded_by?: string;
    description?: string;
}

export interface ClaimStatusHistory {
    id: number;
    claim_id: number;
    status: ClaimStatus;
    status_date: string;
    updated_by?: string;
    notes?: string;
    reason?: string;
}

export interface Claim {
    id: number;
    claim_number: string;
    policy_id: number;
    policy_number: string;
    customer_id: number;

    // Claim Details
    claim_type: ClaimType;
    claim_status: ClaimStatus;
    incident_date: string;
    reported_date: string;

    // Financial Information
    claimed_amount: number;
    approved_amount?: number;
    paid_amount?: number;
    currency: string;

    // Claim Information
    description: string;
    incident_location?: string;
    cause_of_claim?: string;

    // Processing Information
    assigned_adjuster?: string;
    estimated_settlement_date?: string;
    settlement_date?: string;

    // Documents and History
    documents?: ClaimDocument[];
    status_history?: ClaimStatusHistory[];

    // Metadata
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
    notes?: string;
}

export interface PolicyStatistics {
    total_policies: number;
    active_policies: number;
    expired_policies: number;
    pending_policies: number;
    cancelled_policies: number;
    waiting_period_policies: number;
    nearly_expired_policies: number;

    // Financial Statistics
    total_premium_amount: number;
    total_coverage_amount: number;
    average_premium: number;

    // Claims Statistics
    total_claims: number;
    active_claims: number;
    approved_claims: number;
    rejected_claims: number;
    total_claims_amount: number;
    total_paid_amount: number;

    // Policy Type Breakdown
    policy_type_breakdown: Record<PolicyType, number>;

    // Recent Activity
    recent_policies: number; // Last 30 days
    recent_claims: number; // Last 30 days
}

export interface CustomerPoliciesData {
    customer_id: number;
    customer_name: string;
    customer_email: string;
    policies: Policy[];
    claims: Claim[];
    statistics: PolicyStatistics;
    last_updated: string;
}

// Filter interfaces for the policies tab
export interface PolicyFilters {
    status?: PolicyStatus[];
    type?: PolicyType[];
    date_range?: {
        start_date?: string;
        end_date?: string;
    };
    search_query?: string;
    premium_range?: {
        min?: number;
        max?: number;
    };
    coverage_range?: {
        min?: number;
        max?: number;
    };
}

export interface ClaimFilters {
    status?: ClaimStatus[];
    type?: ClaimType[];
    date_range?: {
        start_date?: string;
        end_date?: string;
    };
    search_query?: string;
    amount_range?: {
        min?: number;
        max?: number;
    };
}

export interface PaginatedResponse<T> {
    results: T[];
    count: number;
    next?: string;
    previous?: string;
    page?: number;
    page_size?: number;
    has_more?: boolean;
}

export interface CustomerFilters {
    search?: string;
    platform?: string;
    hasOpenTickets?: boolean;
    page?: number;
    pageSize?: number;
}

export interface CustomerLinkingHistory {
    id: number;
    primary_customer: number;
    linked_customer?: number;
    linking_method: 'CODE' | 'EMAIL' | 'PHONE' | 'MANUAL' | 'AUTO';
    platform_identity?: number;
    status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'EXPIRED';
    linking_code_used?: string;
    failure_reason?: string;
    metadata?: any;
    created_on: string;
    completed_on?: string;
}

export interface CustomerNote {
    id: number;
    customer: number;
    content: string;
    is_active: boolean;
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
}

export interface CustomerMemory {
    id: number;
    customer: number;
    entity_one: string;
    entity_two: string;
    relation_type: string;
    is_important: boolean;
    detail_en: string;
    detail_th: string;
    ticket?: number;
    created_by?: number;
    created_on: string;
    updated_by?: number;
    updated_on: string;
}

// WebSocket event types
export interface CustomerUpdateEvent {
    type: 'customer_update';
    customer: Customer;
}

export interface PlatformStatusUpdateEvent {
    type: 'platform_status_update';
    platform_identity_id: number;
    platform: string;
    status: string;
    channel_name?: string;
}

export interface NewMessageNotificationEvent {
    type: 'new_message_notification';
    customer_id: number;
    platform: string;
    platform_identity_id: number;
    message_preview?: string;
    timestamp: string;
}

export interface MessageStatusUpdateEvent {
    type: 'message_status_update';
    message_id: number;
    status: string;
    timestamp?: string;
}

export interface TypingIndicatorEvent {
    type: 'typing_indicator';
    platform_identity_id: number;
    is_typing: boolean;
    user_name?: string;
}

// API Response interfaces for platform identity operations
export interface PlatformIdentitiesResponse {
    platform_identities: CustomerPlatformIdentity[];
    res_status: number;
    error_msg?: string;
}

export interface PaginatedPlatformIdentitiesResponse {
    results: CustomerPlatformIdentity[];
    count: number;
    next?: string;
    previous?: string;
    res_status: number;
    error_msg?: string;
}

export interface LatestMessagesResponse {
    messages: Record<number, Message>;
    res_status: number;
    error_msg?: string;
}

export interface UnreadCountsResponse {
    unread_counts: Record<number, number>;
    res_status: number;
    error_msg?: string;
}

export interface MarkAsReadResponse {
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface ConversationsResponse {
    messages: Message[];
    has_more: boolean;
    res_status: number;
    error_msg?: string;
}

export interface MarkMessagesAsReadResponse {
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface FocusTicketResponse {
    tickets: any[];
    has_more_older?: boolean;
    has_more_newer?: boolean;
    initial_older_count?: number;
    initial_newer_count?: number;
    res_status: number;
    error_msg?: string;
}