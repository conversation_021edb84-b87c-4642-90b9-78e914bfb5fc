// FAQ-related types for the LLM FAQ service

export interface FaqRequest {
    input: {
        chat_history: string;
        question: string;
    };
    config: Record<string, any>;
    kwargs: Record<string, any>;
}

export interface FaqReference {
    metadata: {
        question: string;
        answer: string;
    };
}

export interface FaqOutput {
    result: string;
    reference: FaqReference[];
}

export interface FaqResponse {
    output: FaqOutput;
    res_status?: number;
    error_msg?: string;
}

export type FaqCategory = 
    | 'All-Features'
    | 'Customer Support'
    | 'Product Search'
    | 'Promotion'
    | 'Recommendation';