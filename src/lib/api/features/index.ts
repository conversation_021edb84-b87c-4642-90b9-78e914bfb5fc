import { TicketService } from "./ticket/tickets.service";
import { MessageService } from "./ticket/messages.service";
import { StatusService } from "./ticket/statuses.service";
import { StatusLogService } from "./ticket/status_logs.service";
import { OwnerLogService } from "./ticket/owner_logs.service"
import { UserService } from "./user/users.service";
import { PartnerService as CompanyService } from "./llm_rag_doc/companies.service";
import { RoleService } from "./user/roles.service";
import { CustomerService } from "./customer/customers.service";
import { DocumentService } from "./llm_rag_doc/documents.service";
import { LineChatBoxService } from "./LineChatBot/lineChatBox.service";
import { PrioritiesService } from "./ticket/priority.service";
import { SystemSettingService } from "./setting/system_settings.service";
import { DepartmentService } from "./user/departments.service";
import { ScheduleService } from "./user/schedule.service";
import { ConnectorService } from "./connector/connectors.service";
import { GuidanceService } from "./llm_rag_doc/guidance.service";
import { SubscriptionService } from "./subscription/subscription.service";

export const services = {
    tickets: new TicketService(),
    messages: new MessageService(),
    statuses: new StatusService(),
    status_logs: new StatusLogService(),
    owner_logs: new OwnerLogService(),
    users: new UserService(),
    companies: new CompanyService(),
    roles: new RoleService(),
    departments: new DepartmentService(),
    schedules: new ScheduleService(),
    customers: new CustomerService(),
    documents: new DocumentService(),
    line_chat_box: new LineChatBoxService(),
    ticket_priority: new PrioritiesService(),
    system_setting: new SystemSettingService(),
    connector: new ConnectorService(),
    guidance: new GuidanceService(),
    subscription: new SubscriptionService()
};
