import { getBackendUrl } from '$src/lib/config';
import type {
    GuidanceRequest,
    GuidanceResponse,
    SearchGuidanceResponse,
    SmartReplyGuidanceResponse
} from '../../types/guidance';
import { ApiError } from '../../client/errors';

export class GuidanceService {
    private baseUrl = `${getBackendUrl()}`;

    /**
     * Search for guidance information from uploaded documents
     * @param query - The search query
     * @param collectionName - The collection/document type to search in
     * @param k - Number of results to return
     * @param token - Authorization token
     * @returns Promise<SearchGuidanceResponse>
     */
    async searchGuidance(
        query: string,
        collectionName: string,
        k: string = '3',
        token: string
    ): Promise<SearchGuidanceResponse> {
        try {
            const requestBody: GuidanceRequest = {
                query,
                collection_name: collectionName,
                k
            };

            const response = await fetch(`${this.baseUrl}/api/guidance/search`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.detail || `HTTP error! status: ${response.status}`,
                    response.status
                );
            }

            const data = await response.json();
            return {
                reply: data.reply || '',
                docs: data.docs || [],
                res_status: response.status
            };
        } catch (error) {
            console.error('Error searching guidance:', error);
            return {
                reply: '',
                docs: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to search guidance'
            };
        }
    }

    /**
     * Generate AI-powered smart reply responses
     * @param query - The query for smart reply
     * @param collectionName - The collection/document type to use
     * @param k - Number of results to return
     * @param token - Authorization token
     * @returns Promise<SmartReplyGuidanceResponse>
     */
    async smartReplyGuidance(
        query: string,
        collectionName: string,
        k: string = '3',
        token: string
    ): Promise<SmartReplyGuidanceResponse> {
        try {
            const requestBody: GuidanceRequest = {
                query,
                collection_name: collectionName,
                k
            };

            const response = await fetch(`${this.baseUrl}/api/guidance/smart_reply`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.detail || `HTTP error! status: ${response.status}`,
                    response.status
                );
            }

            const data = await response.json();
            return {
                reply: data.reply || '',
                docs: data.docs || [],
                res_status: response.status
            };
        } catch (error) {
            console.error('Error generating smart reply:', error);
            return {
                reply: '',
                docs: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to generate smart reply'
            };
        }
    }
}
