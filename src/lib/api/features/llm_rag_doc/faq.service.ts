import type { FaqRequest, FaqResponse, FaqCategory } from "../../types/faq";
import { ApiError } from "../../client/errors";

export class FaqService {
    private baseUrl: string;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    private getEndpointUrl(category: FaqCategory): string {
        switch (category) {
            case 'All-Features':
                return `${this.baseUrl}/faq/invoke`;
            case 'Customer Support':
                return `${this.baseUrl}/faq/customer_support/invoke`;
            case 'Product Search':
                return `${this.baseUrl}/faq/product/invoke`;
            case 'Promotion':
                return `${this.baseUrl}/faq/promotion/invoke`;
            case 'Recommendation':
                return `${this.baseUrl}/recommendation/invoke`;
            default:
                return `${this.baseUrl}/faq/invoke`;
        }
    }

    async sendMessage(question: string, category: FaqCategory = 'All-Features'): Promise<FaqResponse> {
        try {
            const requestBody: FaqRequest = {
                input: {
                    chat_history: '',
                    question: question
                },
                config: {},
                kwargs: {}
            };

            const url = this.getEndpointUrl(category);
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            
            return {
                output: {
                    result: responseData.output?.result || 'No response',
                    reference: responseData.output?.reference || []
                },
                res_status: response.status
            };

        } catch (error) {
            console.error('Error with FAQ API request:', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            
            return {
                output: {
                    result: `Error with API request: ${errorMessage}`,
                    reference: []
                },
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: errorMessage
            };
        }
    }
}