<!--
	@component PolicyDetailModal

	A comprehensive policy detail modal component that displays detailed information
	about an insurance policy including benefits, coverage, claims history, and contract conditions.

	This component is designed to be used with the ModalPortal for full-screen display
	outside of container constraints.

	@example
	```svelte
	<PolicyDetailModal
		bind:isOpen={policyDetailModalOpen}
		{selectedPolicy}
		{mockPolicyDetails}
		on:close={() => policyDetailModalOpen = false}
	/>
	```
-->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import type { Policy } from '$lib/types/customer';
	import { t } from '$lib/stores/i18n';
	import { formatTimestampDMY } from '$lib/utils';
	import { flyAndScale } from '$lib/utils';
	import {
		Button,
		Modal,
	} from 'flowbite-svelte';
	import {
		ClipboardListOutline,
	} from 'flowbite-svelte-icons';

	// Props
	/** Whether the modal is open */
	export let isOpen = false;
	/** The selected policy to display details for */
	export let selectedPolicy: Policy | null = null;
	/** Mock policy details data (will be replaced with real data) */
	export let mockPolicyDetails: any;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	// Debug logging for props
	$: {
		console.log('PolicyDetailModal props changed:', {
			isOpen,
			selectedPolicy: selectedPolicy?.id || 'none',
			policyNumber: selectedPolicy?.policy_number || 'none',
			mockPolicyDetails: !!mockPolicyDetails
		});
	}

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Functions
	function closeModal() {
		console.log('PolicyDetailModal closing...');
		isOpen = false;
		dispatch('close');
		console.log('PolicyDetailModal close event dispatched');
	}
</script>

<!-- Policy Detail Modal -->
<Modal
	bind:open={isOpen}
	size="xl"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto bg-gray-100"
>
	<div slot="header" class="flex flex-col gap-1 space-y-1">
		<div class="flex items-center justify-between">
			<h2 class="flex items-center gap-3 text-lg font-semibold">
				<ClipboardListOutline class="h-6 w-6" />
				{t('policy_modal_header_title')}
				<span
					class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
				>
					{t('policy_status_active')}
				</span>
			</h2>
		</div>
		<div class="flex flex-col gap-1 text-sm text-gray-600 sm:flex-row sm:gap-4">
			<span
				><strong>
					{t('policy_modal_header_member_name')}
				</strong>
				{mockPolicyDetails.member.name}</span
			>
			<span
				><strong>
					{t('policy_modal_header_member_code')}
				</strong>
				{mockPolicyDetails.member.code}</span
			>
		</div>
	</div>

	{#if selectedPolicy}
		<div class="space-y-4 p-1">
			<!-- Policy Information Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white pb-4">
				<h3 class="mb-3 p-4 text-lg font-semibold text-gray-700">
					{t('policy_modal_info_title')}
				</h3>
				<div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm text-gray-600">
							{t('policy_modal_info_policy_no')}
						</span>
						<span class="w-96 font-semibold text-gray-900">{mockPolicyDetails.policy.number}</span>
					</div>
					<div
						class="justify-left flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0"
					>
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_certificate_no')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{mockPolicyDetails.policy.certificateNumber}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_insurance_plan')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{mockPolicyDetails.policy.insurancePlan}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_insurance_company')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{mockPolicyDetails.policy.insuranceCompany}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_effective_from')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{formatTimestampDMY(mockPolicyDetails.policy.effectiveFrom)}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_effective_to')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{formatTimestampDMY(mockPolicyDetails.policy.effectiveTo)}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_contract_company')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{mockPolicyDetails.policy.contractCompany}
						</span>
					</div>
					<div class="flex flex-col space-y-1 px-4 sm:flex-row sm:justify-between sm:space-y-0">
						<span class="w-xl text-sm ttext-gray-600">
							{t('policy_modal_info_premium')}
						</span>
						<span class="w-96 font-semibold text-gray-900">
							{mockPolicyDetails.policy.premium}
						</span>
					</div>
				</div>
			</div>

			<!-- Main Benefits Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-700">
                        {t('policy_modal_benefit_title')}
                    </h3>
                    <span class="text-sm text-gray-600">(หน่วย:บาท)</span>
                </div>
				<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
					<!-- Left side - Description -->
					<div class="mb-3 lg:mb-0 lg:flex-1">
						<h4 class="mb-1 text-base text-lg font-semibold text-gray-900">
							{mockPolicyDetails.policy.insurancePlan}
						</h4>
						<p class="text-sm leading-relaxed text-gray-600">
							{mockPolicyDetails.mainBenefits.description}
						</p>
					</div>

					<!-- Right side - Amount -->
					<div
						class="rounded-lg bg-gray-50 p-3 shadow-sm lg:ml-6 lg:text-right"
					>
                        <span class="mb-1 text-sm text-gray-600">
                            {mockPolicyDetails.mainBenefits.period}
                        </span>
						<div class="mb-1">
							<span class="text-xl font-bold text-blue-600 sm:text-xl lg:text-2xl">
								{mockPolicyDetails.mainBenefits.amount.toLocaleString()}
								{mockPolicyDetails.mainBenefits.currency}
							</span>
						</div>
						<div class="text-sm text-gray-600">
							<span>
								{t('policy_modal_benefit_remaining')}
							</span>
							<span class="font-bold text-green-600">
								{mockPolicyDetails.mainBenefits.currency}
								{mockPolicyDetails.mainBenefits.remaining.toLocaleString()}
							</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Detailed Coverage Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-lg text-gray-700">
                        {t('policy_modal_coverage_title')}
                    </h3>
                    <span class="text-sm text-gray-600">(หน่วย:บาท)</span>
                </div>
				<div class="space-y-3">
					{#each mockPolicyDetails.detailedCoverage as coverage}
						<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
							<div class="mb-0 flex flex-col items-start justify-between lg:flex-row">
								<div class="mb-3 flex-1 lg:mb-0">
									<h4 class="font-semibold text-gray-900">{coverage.type}</h4>
									<p class="text-sm text-gray-600">{coverage.description}</p>
								</div>

								<!-- Financial Elements positioned at top-right -->
								<div
									class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 text-sm shadow-sm sm:flex-row sm:justify-between md:gap-6 lg:justify-end lg:gap-6"
								>
                                    <div class="w-20 flex flex-col sm:text-right">
                                        <div class="text-xs text-gray-600">
                                            {t('policy_modal_coverage_limit_annual')}
                                        </div>
                                        <div class="text-xl font-bold text-gray-600">
                                            {coverage.annualLimit.toLocaleString()}
                                        </div>
                                    </div>
									<div class="w-20 flex flex-col sm:text-right">
										<div class="text-xs text-gray-600">
											{t('policy_modal_coverage_limit_visit')}
										</div>
										<div class="text-xl font-bold text-gray-600">
											{coverage.limit.toLocaleString()}
										</div>
									</div>
									<div class="w-20 flex flex-col sm:text-right">
										<div class="text-xs text-gray-600">
											{t('policy_modal_coverage_remaining')}
										</div>
										<div class="text-xl font-bold text-green-600">
											{coverage.remaining.toLocaleString()}
										</div>
									</div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Contract Conditions Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<div class="flex items-center justify-between">
					<h3 class="mb-3 text-lg font-semibold text-gray-700">
						{t('policy_modal_condition_title')}
					</h3>
				</div>
				<div class="rounded-lg border border-gray-200 py-4 shadow-sm">
					<div class="mb-2 flex flex-col gap-2 px-4 sm:flex-row sm:items-center sm:gap-3">
						<h4 class="font-semibold text-gray-900">
							{mockPolicyDetails.contractConditions.memberType}
						</h4>
						<div class="flex flex-wrap gap-2">
							<!-- Status Badge -->
							<span
								class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
							>
								{t('policy_status_active')}
							</span>
						</div>
					</div>
					<div class="space-y-2">
						<div class="flex justify-start px-4">
							<span class="text-sm w-32 text-gray-600">
								{t('policy_modal_condition_remarks')}
							</span>
							<span class="text-sm font-semibold text-gray-900"
								>{mockPolicyDetails.contractConditions.ageRange}</span
							>
						</div>
						<div class="flex justify-start px-4">
							<span class="text-sm w-32 text-gray-600">
								{t('policy_modal_condition_effective_date')}
							</span>
							<span class="text-sm font-semibold text-gray-900">
								{formatTimestampDMY(mockPolicyDetails.contractConditions.startDate)}
							</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Claims History Timeline Section -->
			<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
				<div class="mb-4 flex flex-col items-start justify-between sm:flex-row sm:items-center">
					<h3 class="mb-2 text-lg font-semibold text-gray-700 sm:mb-0">
						{t('policy_modal_claims_title')}
					</h3>
					<div class="text-sm text-gray-600">
						{t('policy_modal_claims_total')}
						{mockPolicyDetails.claimsHistory.length}
						{t('policy_modal_claims_total_unit')}
                        (หน่วย:บาท)
					</div>
				</div>
				
				<!-- Timeline Container -->
				<div class="relative">
					<!-- Timeline line - vertical line connecting all items -->
					<div class="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-300"></div>
					
					<!-- Timeline Items -->
					<div class="space-y-6">
						{#each mockPolicyDetails.claimsHistory as claim, index}
							<div class="relative flex items-start">
								<!-- Timeline dot/icon -->
								<div class="relative z-10 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-white shadow-lg
									{claim.status === 'Paid'
										? 'ring-2 ring-green-500 bg-green-100'
										: claim.status === 'Pending'
											? 'ring-2 ring-blue-500 bg-blue-100'
											: claim.status === 'Rejected'
												? 'ring-2 ring-red-500 bg-red-100'
												: 'ring-2 ring-yellow-500 bg-yellow-100'}">
									<!-- Status icon -->
									{#if claim.status === 'Paid'}
										<svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
										</svg>
									{:else if claim.status === 'Pending'}
										<svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
										</svg>
									{:else if claim.status === 'Rejected'}
										<svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
										</svg>
									{:else}
										<svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
										</svg>
									{/if}
								</div>
								
								<!-- Timeline content -->
								<div class="ml-6 min-w-0 flex-1">
									<!-- Main claim card -->
									<div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm hover:shadow transition-shadow">
										<!-- Claim header -->
										<div class="mb-3 flex flex-col items-start justify-between lg:flex-row">
											<div class="mb-3 flex-1 lg:mb-0">
												<div class="mb-2 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
													<h4 class="text-lg font-semibold text-gray-900">
														<!-- {t('policy_modal_claims_no')} -->
														{claim.claimNo}
													</h4>
													<div class="flex flex-wrap gap-2">
														<!-- Status Badge -->
														<span
															class="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium
															{claim.status === 'Paid'
																? 'border-green-200 bg-green-100 text-green-800'
																: claim.status === 'Reimbursed'
																	? 'border-yellow-200 bg-yellow-100 text-yellow-800'
																	: claim.status === 'Pending'
																		? 'border-blue-200 bg-blue-100 text-blue-800'
																		: claim.status === 'Rejected'
																			? 'border-red-200 bg-red-100 text-red-800'
																			: 'border-blue-200 bg-blue-100 text-blue-800'}"
														>
															{t('policy_modal_claims_status_' + claim.status.toLowerCase())}
														</span>
														<!-- Type Badge -->
														<span
															class="inline-flex items-center rounded-full border border-gray-200 bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800"
														>
															{t('policy_modal_claims_type_' + claim.type.toLowerCase())}
														</span>
													</div>
												</div>
												<p class="text-gray-700">{claim.diagnosis}</p>
											</div>

											<!-- Amount Information -->
											<div
												class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 text-sm shadow-sm sm:flex-row sm:justify-between md:gap-6 lg:justify-end lg:gap-6"
											>
												<div class="w-20 flex flex-col sm:text-right">
													<div class="text-xs text-gray-600">
														{t('policy_modal_claims_amount_claimed')}
													</div>
													<div class="text-xl font-bold text-gray-600">
														{claim.claimedAmount.toLocaleString()}
													</div>
												</div>
												<div class="w-20 flex flex-col sm:text-right">
													<div class="text-xs text-gray-600">
														{t('policy_modal_claims_amount_paid')}
													</div>
													<div class="text-xl font-bold text-gray-600">
														{claim.paidAmount.toLocaleString()}
													</div>
												</div>
												<div class="w-20 flex flex-col sm:text-right">
													<div class="text-xs text-gray-600">
														{t('policy_modal_claims_amount_diff')}
													</div>
													<div class="text-xl font-bold text-green-600">
														{claim.difference.toLocaleString()}
													</div>
												</div>
											</div>
										</div>

										<!-- Claim Details Grid -->
										<div class="mt-3 grid gap-3 text-sm sm:grid-cols-2 lg:grid-cols-4">
											<!-- Service Date -->
											<div class="flex items-start">
												<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
												</svg>
												<div>
													<div class="font-medium text-gray-700">
														{t('policy_modal_claims_date_service')}
													</div>
													<div class="text-gray-600">{formatTimestampDMY(claim.serviceDate)}</div>
												</div>
											</div>

											<!-- Claim Date -->
											<div class="flex items-start">
												<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
												</svg>
												<div>
													<div class="font-medium text-gray-700">
														{t('policy_modal_claims_date_claim')}
													</div>
													<div class="text-gray-600">{formatTimestampDMY(claim.claimDate)}</div>
												</div>
											</div>

											<!-- Settlement Date -->
											<div class="flex items-start">
												<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
												</svg>
												<div>
													<div class="font-medium text-gray-700">
														{t('policy_modal_claims_date_settlement')}
													</div>
													<div class="text-gray-600">
														{(claim.settlementDate? formatTimestampDMY(claim.settlementDate) : '-' )}
													</div>
												</div>
											</div>

											<!-- Provider Information -->
											<div class="flex items-start">
												<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
												</svg>
												<div>
													<div class="font-medium text-gray-700">
														{t('policy_modal_claims_provider')}
													</div>
													<div class="text-gray-600">{claim.provider}</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<div class="modal-footer-right">
			<Button color="light" class="ml-auto" on:click={closeModal}>
				{t('policy_modal_close')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>

<style>
	/* Modal content animations with accessibility support */
	:global(.modal-content-section) {
		animation: fadeInUp 0.3s ease-out;
		animation-fill-mode: both;
	}

	:global(.modal-content-section:nth-child(1)) {
		animation-delay: 0.05s;
	}
	:global(.modal-content-section:nth-child(2)) {
		animation-delay: 0.1s;
	}
	:global(.modal-content-section:nth-child(3)) {
		animation-delay: 0.15s;
	}
	:global(.modal-content-section:nth-child(4)) {
		animation-delay: 0.2s;
	}
	:global(.modal-content-section:nth-child(5)) {
		animation-delay: 0.25s;
	}
	:global(.modal-content-section:nth-child(6)) {
		animation-delay: 0.3s;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Respect user's motion preferences */
	@media (prefers-reduced-motion: reduce) {
		:global(.modal-content-section) {
			animation: none;
		}

		@keyframes fadeInUp {
			from,
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	}

	/* Modal footer button alignment - target the specific modal footer */
	:global(.modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
	}

	/* Additional targeting for Flowbite Modal footer */
	:global([data-modal-target] .modal-footer-right),
	:global(.fixed .modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
		margin-left: auto !important;
	}
</style>