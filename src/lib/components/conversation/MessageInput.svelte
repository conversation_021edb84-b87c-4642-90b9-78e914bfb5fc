<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import { templates as templatesStore, templateActions } from '$lib/stores/quickResponseTemplates';
	import { validateAndSanitizeMessage } from '$lib/utils/messageSanitizer';
	import { conversationService } from '$lib/services/conversationService';
	import type { FileUploadState, UploadedFileMetadata } from '$lib/types/customer';
	import { UploadOutline } from 'flowbite-svelte-icons';

	const dispatch = createEventDispatcher();

	export let disabled = false;
	export let canSendMessage = true;
	export let isNotTicketOwner = false;
	export let isTicketPendingToClose = false;
	export let isTicketClosed = false;
	export let conversationId: string | number | null = null; // Identifier for current conversation
	export let customerId: number;
	export let platformId: number;

	let messageText = '';
	let isTyping = false;
	let typingTimeout: number | null = null;
	let fileInput: HTMLInputElement;
	let fileUploadStates: FileUploadState[] = [];
	let showFilePreview = false;
	let isDragOver = false;
	let isUploading = false;
	let removingFileIndex: number | null = null;
	let isClearingAll = false;

	// File upload limits
	const MAX_FILES = 5;
	const MAX_INDIVIDUAL_FILE_SIZE = 25 * 1024 * 1024; // 25 MB
	const MAX_TOTAL_SIZE = 100 * 1024 * 1024; // 100 MB

	let showSuggestions = false;
	let filteredSuggestions: Array<{
		id: string;
		keyword: string;
		template: string;
		description: string;
	}> = [];
	let selectedSuggestionIndex = -1;
	let textareaElement: HTMLTextAreaElement;
	let suggestionDropdown: HTMLElement;
	let currentTriggerWord = '';
	let triggerStartPosition = -1;
	let isSending = false;

	// Validation state
	let validationError = '';
	$: hasValidationError = canSendMessage ? validationError.length > 0 : false;

	// Track previous conversation ID to detect changes
	let previousConversationId = conversationId;

	// Clear validation error and reset component state when conversation changes
	$: if (conversationId !== previousConversationId) {
		// Clear validation error state - this hides the red error bar immediately
		validationError = '';

		// Reset message input state for clean slate in new conversation
		messageText = '';
		fileUploadStates = [];
		showFilePreview = false;
		isDragOver = false;
		isUploading = false;
		removingFileIndex = null;
		isClearingAll = false;

		// Hide suggestions dropdown to prevent showing suggestions from previous conversation
		hideSuggestions();

		// Stop typing indicator to clean up previous conversation state
		stopTyping();

		// Update tracking variable to prevent infinite reactive loop
		previousConversationId = conversationId;
	}

	$: responseTemplates = $templatesStore;

	onMount(async () => {
		await templateActions.loadTemplates();
	});

	function validateCurrentMessage() {
		if (!messageText.trim()) {
			validationError = '';
			return;
		}

		const result = validateAndSanitizeMessage(messageText);

		if (!result.isValid) {
			validationError = result.error;
			messageText = '';
		} else {
			validationError = '';
		}
	}

	function handleSend() {
		// Validate and sanitize message before sending
		const result = validateAndSanitizeMessage(messageText);

		if (!result.isValid) {
			validationError = result.error;
			return;
		}

		// Get only successfully uploaded files
		const uploadedFiles = fileUploadStates
			.filter(state => state.status === 'uploaded' && state.uploadedMetadata)
			.map(state => state.uploadedMetadata!);

		console.log('Sending message:', result.sanitizedContent, uploadedFiles);

		if ((result.sanitizedContent || uploadedFiles.length > 0) && !disabled && canSendMessage && !hasValidationError) {
			dispatch('send', {
				content: result.sanitizedContent,
				type: 'TEXT',
				preUploadedFiles: uploadedFiles
			});

			// Reset state
			messageText = '';
			fileUploadStates = [];
			showFilePreview = false;
			validationError = '';
			stopTyping();
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (disabled || !canSendMessage) {
			event.preventDefault();
			return;
		}

		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleSend();
		}
	}

	function handleInput(event: Event) {
		if (disabled || !canSendMessage) {
			return;
		}

		if (!isTyping) {
			isTyping = true;
			dispatch('typing', { isTyping: true });
		}

		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}

		typingTimeout = window.setTimeout(() => {
			stopTyping();
		}, 1000);

		// Check for suggestions
		// checkForSuggestions();

		// Validate message content
		validateCurrentMessage();
	}

	function checkForSuggestions() {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const textBeforeCursor = messageText.substring(0, cursorPosition);

		// Find the last word before cursor
		const words = textBeforeCursor.split(/\s+/);
		const lastWord = words[words.length - 1].toLowerCase();

		if (lastWord.length >= 2) {
			// Find matching templates
			const matches = responseTemplates.filter(
				(template) =>
					template.keyword.toLowerCase().includes(lastWord) ||
					template.description.toLowerCase().includes(lastWord)
			);

			if (matches.length > 0) {
				filteredSuggestions = matches;
				currentTriggerWord = lastWord;
				triggerStartPosition = cursorPosition - lastWord.length;
				selectedSuggestionIndex = -1;
				showSuggestions = true;
			} else {
				hideSuggestions();
			}
		} else {
			hideSuggestions();
		}
	}

	function selectSuggestion(suggestion: {
		id: string;
		keyword: string;
		template: string;
		description: string;
	}) {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const beforeTrigger = messageText.substring(0, triggerStartPosition);
		const afterCursor = messageText.substring(cursorPosition);

		// Replace the trigger word with the template
		messageText = beforeTrigger + suggestion.template + afterCursor;

		// Hide suggestions first
		hideSuggestions();

		// Set cursor position after the inserted template and return focus to textarea
		setTimeout(() => {
			const newCursorPosition = beforeTrigger.length + suggestion.template.length;
			textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
			textareaElement.focus();
		}, 0);
	}

	function hideSuggestions() {
		showSuggestions = false;
		filteredSuggestions = [];
		selectedSuggestionIndex = -1;
		currentTriggerWord = '';
		triggerStartPosition = -1;
	}

	function hideSuggestionsAndReturnFocus() {
		hideSuggestions();
		// Return focus to textarea
		setTimeout(() => {
			if (textareaElement) {
				textareaElement.focus();
			}
		}, 0);
	}

	function scrollToSelectedSuggestion() {
		if (!suggestionDropdown || selectedSuggestionIndex < 0) return;

		const selectedElement = suggestionDropdown.children[selectedSuggestionIndex] as HTMLElement;
		if (selectedElement) {
			selectedElement.scrollIntoView({ block: 'nearest' });
		}
	}

	function handleClickOutside(event: MouseEvent) {
		if (
			suggestionDropdown &&
			!suggestionDropdown.contains(event.target as Node) &&
			!textareaElement.contains(event.target as Node)
		) {
			hideSuggestionsAndReturnFocus();
		}
	}

	function stopTyping() {
		if (isTyping) {
			isTyping = false;
			dispatch('typing', { isTyping: false });
		}
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
			typingTimeout = null;
		}
	}

	function handleFileClick() {
		if (disabled || !canSendMessage) return;
		fileInput.click();
	}

	async function handleFileSelect(event: Event) {
		if (disabled || !canSendMessage) return;

		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			const newFiles = Array.from(input.files);

			// Validate files before upload
			const validation = validateFileUpload(newFiles);
			if (!validation.isValid) {
				alert(validation.errorMessage);
				input.value = ''; // Clear input to allow re-selection
				return;
			}

			await uploadFilesImmediate(newFiles);
			input.value = '';
		}
	}

	async function uploadFilesImmediate(files: File[]) {
		if (!files.length || disabled || !canSendMessage) return;

		// Add files to state with uploading status
		const newUploadStates = files.map(file => ({
			file,
			status: 'uploading' as const,
			progress: 0
		}));

		fileUploadStates = [...fileUploadStates, ...newUploadStates];
		isUploading = true;
		showFilePreview = true;

		try {
			// Upload files using the conversation service
			const response = await conversationService.uploadFilesImmediate(
				customerId,
				platformId,
				files
			);

			// Create maps for efficient file matching by original name
			const uploadedFileMap = new Map(
				response.uploaded_files.map(file => [file.original_name, file])
			);
			const failedFileMap = new Map(
				response.failed_files.map(file => [file.original_name, file])
			);

			// Debug logging for partial success scenarios
			console.log('Upload response summary:', {
				total_attempted: response.summary?.total_attempted || files.length,
				successful: response.summary?.successful || response.uploaded_files.length,
				failed: response.summary?.failed || response.failed_files.length,
				uploaded_files: response.uploaded_files.map(f => f.original_name),
				failed_files: response.failed_files.map(f => ({ name: f.original_name, error: f.error }))
			});

			// Update each file state individually based on API response
			const startIndex = fileUploadStates.length - files.length;
			files.forEach((file, index) => {
				const stateIndex = startIndex + index;
				if (!fileUploadStates[stateIndex]) {
					console.warn(`File state not found at index ${stateIndex} for file: ${file.name}`);
					return;
				}

				// Check if this file was uploaded successfully
				const uploadedFile = uploadedFileMap.get(file.name);
				if (uploadedFile) {
					fileUploadStates[stateIndex] = {
						...fileUploadStates[stateIndex],
						status: 'uploaded',
						uploadedMetadata: uploadedFile,
						error: undefined // Clear any previous error
					};
					return;
				}

				// Check if this file failed with specific error message
				const failedFile = failedFileMap.get(file.name);
				if (failedFile) {
					fileUploadStates[stateIndex] = {
						...fileUploadStates[stateIndex],
						status: 'failed',
						error: failedFile.error || 'Upload failed'
					};
					return;
				}

				// If file is not in either list, mark as failed with generic error
				// This handles edge cases where the API response doesn't include the file
				fileUploadStates[stateIndex] = {
					...fileUploadStates[stateIndex],
					status: 'failed',
					error: 'Upload status unknown - file not found in response'
				};
			});

		} catch (error: any) {
			// Extract specific error message from API response or use generic message
			let errorMessage = 'Upload failed';

			// Try to extract error message from different possible response structures
			if (error?.message) {
				errorMessage = error.message;
			} else if (error?.response?.data?.error) {
				errorMessage = error.response.data.error;
			} else if (error?.response?.data?.message) {
				errorMessage = error.response.data.message;
			} else if (typeof error === 'string') {
				errorMessage = error;
			}

			// Mark all files as failed with the specific error message
			newUploadStates.forEach((_, index) => {
				const stateIndex = fileUploadStates.length - files.length + index;
				if (fileUploadStates[stateIndex]) {
					fileUploadStates[stateIndex] = {
						...fileUploadStates[stateIndex],
						status: 'failed',
						error: errorMessage
					};
				}
			});
		} finally {
			isUploading = false;
			fileUploadStates = [...fileUploadStates]; // Trigger reactivity
		}
	}

	async function removeFile(index: number) {
		const fileState = fileUploadStates[index];

		// Set loading state for visual feedback
		removingFileIndex = index;

		// If file was uploaded, delete from server
		if (fileState.status === 'uploaded' && fileState.uploadedMetadata) {
			try {
				await conversationService.deleteUploadedFile(
					customerId,
					platformId,
					fileState.uploadedMetadata.metadata.blob_path
				);
			} catch (error) {
				console.error('Failed to delete file from server:', error);
				// Continue with local removal even if server deletion fails
			}
		}

		// Clear loading state and remove file from local state
		removingFileIndex = null;
		fileUploadStates = fileUploadStates.filter((_, i) => i !== index);
		if (fileUploadStates.length === 0) {
			showFilePreview = false;
		}
	}

	async function clearAllFiles() {
		// Set loading state for visual feedback
		isClearingAll = true;

		// Delete uploaded files from server before clearing local state
		const uploadedFiles = fileUploadStates.filter(
			state => state.status === 'uploaded' && state.uploadedMetadata
		);

		// Attempt to delete each uploaded file from server
		for (const fileState of uploadedFiles) {
			if (fileState.uploadedMetadata) {
				try {
					await conversationService.deleteUploadedFile(
						customerId,
						platformId,
						fileState.uploadedMetadata.metadata.blob_path
					);
				} catch (error) {
					console.error('Failed to delete file from server:', error);
					// Continue with deletion of other files even if one fails
				}
			}
		}

		// Clear loading state and local state after attempting server deletions
		isClearingAll = false;
		fileUploadStates = [];
		showFilePreview = false;
	}

	function handleDragEnter(event: DragEvent) {
		if (disabled || !canSendMessage) return;
		event.preventDefault();
		event.stopPropagation();
		isDragOver = true;
	}

	function handleDragOver(event: DragEvent) {
		if (disabled || !canSendMessage) return;
		event.preventDefault();
		event.stopPropagation();
		// Ensure drag over state is maintained
		isDragOver = true;
	}

	function handleDragLeave(event: DragEvent) {
		if (disabled || !canSendMessage) return;
		event.preventDefault();
		event.stopPropagation();

		// Only set isDragOver to false if we're actually leaving the component
		// Check if the related target is outside the component
		const currentTarget = event.currentTarget as HTMLElement;
		const relatedTarget = event.relatedTarget as HTMLElement;

		if (!currentTarget.contains(relatedTarget)) {
			isDragOver = false;
		}
	}

	async function handleDrop(event: DragEvent) {
		if (disabled || !canSendMessage) return;
		event.preventDefault();
		isDragOver = false;

		const files = Array.from(event.dataTransfer?.files || []);
		if (files.length > 0) {
			// Validate files before upload
			const validation = validateFileUpload(files);
			if (!validation.isValid) {
				alert(validation.errorMessage);
				return;
			}

			await uploadFilesImmediate(files);
		}
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	function formatErrorMessage(error: string): string {
		// Truncate very long error messages and make them user-friendly
		const maxLength = 100;
		let formattedError = error || 'Upload failed';

		// Clean up common API error patterns
		formattedError = formattedError
			.replace(/^Error:\s*/i, '') // Remove "Error: " prefix
			.replace(/^Failed to upload files:\s*/i, '') // Remove redundant prefix
			.trim();

		// Truncate if too long
		if (formattedError.length > maxLength) {
			formattedError = formattedError.substring(0, maxLength - 3) + '...';
		}

		// Capitalize first letter if it's not already
		if (formattedError.length > 0) {
			formattedError = formattedError.charAt(0).toUpperCase() + formattedError.slice(1);
		}

		return formattedError;
	}

	function calculateExistingFilesSize(): number {
		return fileUploadStates
			.filter(state => state.status === 'uploaded' && state.uploadedMetadata)
			.reduce((total, state) => {
				return total + (state.uploadedMetadata?.metadata?.size || 0);
			}, 0);
	}

	function calculateNewFilesSize(files: File[]): number {
		return files.reduce((total, file) => total + file.size, 0);
	}

	function formatFileSizeInMB(bytes: number): string {
		return (bytes / (1024 * 1024)).toFixed(1) + 'MB';
	}

	function validateFileUpload(newFiles: File[]): { isValid: boolean; errorMessage?: string } {
		// 1. File count validation
		const totalFiles = fileUploadStates.length + newFiles.length;
		if (totalFiles > MAX_FILES) {
			return {
				isValid: false,
				// errorMessage: `Maximum ${MAX_FILES} files allowed per message. Please remove existing files or select fewer files.`
				errorMessage: t('chat_center_attachment_file_count_exceeded').replace('MAX_FILES', MAX_FILES.toString())
			};
		}

		// 2. Individual file size validation
		for (const file of newFiles) {
			if (file.size > MAX_INDIVIDUAL_FILE_SIZE) {
				return {
					isValid: false,
					// errorMessage: `File '${file.name}' is too large (${formatFileSize(file.size)}). Maximum individual file size is ${formatFileSize(MAX_INDIVIDUAL_FILE_SIZE)}. Please select a smaller file.`
					errorMessage: t('chat_center_attachment_individual_file_size_exceeded').replace('MAX_INDIVIDUAL_FILE_SIZE', formatFileSize(MAX_INDIVIDUAL_FILE_SIZE))
				};
			}
		}

		// 3. Total file size validation
		const existingSize = calculateExistingFilesSize();
		const newFilesSize = calculateNewFilesSize(newFiles);
		const totalSize = existingSize + newFilesSize;

		if (totalSize > MAX_TOTAL_SIZE) {
			return {
				isValid: false,
				// errorMessage: `Total file size cannot exceed 100MB. Current: ${formatFileSizeInMB(existingSize)}, Selected: ${formatFileSizeInMB(newFilesSize)}. Please remove files or select smaller files.`
				errorMessage: t('chat_center_attachment_total_file_size_exceeded').replace('MAX_TOTAL_SIZE', formatFileSize(MAX_TOTAL_SIZE))
			};
		}

		return { isValid: true };
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleClickOutside);
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}
	});

	// Handle paste events
	function handlePaste(event: ClipboardEvent) {
		if (disabled || !canSendMessage) {
			event.preventDefault();
			return;
		}

		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';

		// Insert pasted text at cursor position
		const cursorPosition = textareaElement.selectionStart;
		const beforeCursor = messageText.substring(0, cursorPosition);
		const afterCursor = messageText.substring(textareaElement.selectionEnd);

		messageText = beforeCursor + pastedText + afterCursor;

		// Validate message content after paste
		validateCurrentMessage();

		// Set cursor position after pasted content
		setTimeout(() => {
			const newPosition = cursorPosition + pastedText.length;
			textareaElement.setSelectionRange(newPosition, newPosition);
			textareaElement.focus();
		}, 0);
	}
</script>

{#if !canSendMessage}
	<div class="border-t border-gray-200 bg-yellow-50 px-4 py-3">
		<div class="flex items-center">
			<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
			</svg>
			<span class="ml-2 text-sm text-yellow-800">
				{#if isNotTicketOwner}
					{t('input_error_not_ticket_owner')}
				{:else if isTicketPendingToClose}
					<p>{t('input_error_ticket_pending_to_close')} • {t('input_error_create_new_ticket')}</p>
				{:else if isTicketClosed}
					<p>{t('input_error_ticket_closed')} • {t('input_error_create_new_ticket')}</p>
				{/if}
			</span>
		</div>
	</div>
{/if}

{#if hasValidationError && canSendMessage && !isNotTicketOwner && !isTicketPendingToClose && !isTicketClosed}
	<div class="border-t border-gray-200 bg-red-50 px-4 py-3">
		<div class="flex items-center">
			<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
			</svg>
			<span class="ml-2 text-sm text-red-800">
				{validationError}
			</span>
		</div>
	</div>
{/if}

{#if showFilePreview && fileUploadStates.length > 0}
	<div class="border-t border-gray-200 bg-gray-50 px-4 py-3"
		 role="region"
		 aria-label="File upload area">

		<div class="mb-3 flex items-center justify-between">
			<span class="text-sm font-medium text-gray-700">
				{t('chat_center_attachment_label_count')} ({fileUploadStates.length}/{MAX_FILES}):
			</span>
			<!-- Clear All button -->
			<!-- {#if fileUploadStates.length > 0 && !isUploading}
				<button
					id="message-input-clear-files-button"
					on:click={clearAllFiles}
					class="text-sm text-red-600 hover:text-red-700 flex items-center space-x-1"
					disabled={disabled || !canSendMessage || isUploading || isClearingAll}>
					{#if isClearingAll}
						<div class="animate-spin rounded-full h-3 w-3 border border-red-600 border-t-transparent"></div>
						<span>{t('chat_center_attachment_clearing_all')}</span>
					{:else}
						<span>{t('chat_center_attachment_clear_all')}</span>
					{/if}
				</button>
			{/if} -->
		</div>

		<div class="space-y-2 overflow-y-auto">
			{#each fileUploadStates as fileState, index}
				<div class="relative rounded-lg border bg-white p-3 shadow-sm
						   {fileState.status === 'uploaded' ? 'border-green-200 bg-green-50' :
							fileState.status === 'failed' ? 'border-red-200 bg-red-50' :
							'border-blue-200 bg-blue-50'}">

					<div class="flex items-center justify-between">
						<div class="min-w-0 flex-1">
							<div class="flex items-center space-x-2">
								<!-- File type icon or thumbnail - only show thumbnails for images -->
								{#if fileState.file.type.startsWith('image/')}
									<img src={URL.createObjectURL(fileState.file)}
										 alt="Preview"
										 class="h-8 w-8 rounded object-cover" />
								{:else}
									<!-- File type icon for non-image files (including PDFs) -->
									<div class="h-8 w-8 rounded bg-gray-200 flex items-center justify-center">
										<svg class="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
										</svg>
									</div>
								{/if}

								<div class="min-w-0 flex-1">
									<div class="truncate text-sm font-medium text-gray-900">
										{fileState.file.name}
									</div>
									<div class="text-xs text-gray-500">
										{formatFileSize(fileState.file.size)}
									</div>
								</div>
							</div>

							<!-- Upload progress or status -->
							{#if fileState.status === 'uploading'}
								<div class="mt-2">
									<div class="flex items-center space-x-2">
										<!-- Spinner animation -->
										<div class="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
										<span class="text-xs text-gray-500">{t('chat_center_attachment_uploading')}</span>
									</div>
								</div>
							{:else if fileState.status === 'uploaded'}
								<div class="mt-1 text-xs text-green-600 flex items-center">
									<svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
									</svg>
									<span>{t('chat_center_attachment_upload_success')}</span>
								</div>
							{:else if fileState.status === 'failed'}
								<div class="mt-1 text-xs text-red-600 flex items-start">
									<svg class="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
									</svg>
									<span
										class="break-words leading-tight cursor-help"
										title={fileState.error || 'Upload failed'}>
										{formatErrorMessage(fileState.error || 'Upload failed')}
									</span>
								</div>
							{/if}
						</div>

						{#if fileState.status !== 'uploading' && !isClearingAll}
							<button
								id="message-input-remove-file-button-{index}"
								on:click={() => removeFile(index)}
								class="ml-2 rounded-full p-1 transition-colors {fileState.status === 'uploading' || removingFileIndex === index
									? 'text-gray-400 cursor-not-allowed'
									: 'text-red-500 hover:bg-red-50 hover:text-red-700'}"
								title={t('chat_center_attachment_remove_file')}
								disabled={disabled || !canSendMessage || fileState.status === 'uploading' || removingFileIndex === index}>
								{#if removingFileIndex === index}
									<div class="animate-spin rounded-full h-4 w-4 border border-gray-400 border-t-transparent"></div>
								{:else}
									<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
									</svg>
								{/if}
							</button>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	</div>
{/if}

<div class="relative border-t border-gray-200 bg-white px-4 py-3 {!canSendMessage ? 'opacity-50' : ''}"
	 role="application"
	 aria-label="Message input with file drop zone"
	 on:dragenter={handleDragEnter}
	 on:dragover={handleDragOver}
	 on:dragleave={handleDragLeave}
	 on:drop={handleDrop}>

	{#if isDragOver}
		<div class="absolute inset-1 bg-blue-50 border-2 border-dashed border-blue-300
					flex items-center justify-center z-50 rounded-lg">
			<div class="flex flex-col items-center text-center">
				<div><UploadOutline class="h-6 w-6 text-blue-600" /></div>
				<p class="mt-1 text-sm font-medium text-blue-600">{t('chat_center_attachment_drop_here')}</p>
			</div>
		</div>
	{/if}

	<div class="flex items-center space-x-3">
		<button
			id="message-input-attachment-button"
			on:click={handleFileClick}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors hover:bg-gray-100 {disabled || !canSendMessage ? 'cursor-not-allowed opacity-50' : ''}"
			title={t('chat_center_attachment_attach')}
			disabled={disabled || !canSendMessage}
		>
			<svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
				/>
			</svg>
		</button>

		<div class="flex min-w-0 flex-1">
			{#if showSuggestions && filteredSuggestions.length > 0}
				<div
					id="message-input-suggestion-dropdown"
					bind:this={suggestionDropdown}
					class="suggestion-dropdown absolute bottom-full left-0 right-0 z-50 mb-2 max-h-60 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"
				>
					{#each filteredSuggestions as suggestion, index}
						<button
							id="message-input-suggestion-{index}"
							class="w-full border-b border-gray-100 px-4 py-3 text-left transition-all duration-150 last:border-b-0 hover:bg-gray-50 {index ===
							selectedSuggestionIndex
								? 'border-blue-200 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50'
								: ''}"
							on:click={() => selectSuggestion(suggestion)}
							on:mouseenter={() => (selectedSuggestionIndex = index)}
						>
							<div class="flex items-start justify-between">
								<div class="min-w-0 flex-1">
									<div class="truncate text-sm font-medium text-gray-900">
										{suggestion.keyword}
									</div>
								</div>
								<div class="ml-2 flex-shrink-0">
									<span
										class="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
									>
										Template
									</span>
								</div>
							</div>
							<div class="mt-2 line-clamp-2 text-xs text-gray-600">
								{suggestion.template}
							</div>
						</button>
					{/each}
				</div>
			{/if}

			<textarea
				id="message-input-textarea"
				bind:this={textareaElement}
				bind:value={messageText}
				on:keydown={handleKeyPress}
				on:input={handleInput}
				on:paste={handlePaste}
				placeholder={canSendMessage ? t('type_message') : t('cannot_send_ticket_message')}
				rows="1"
				class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 {disabled || !canSendMessage ? 'cursor-not-allowed bg-gray-100' : ''}"
				style="min-height: 40px; max-height: 120px;"
				disabled={disabled || !canSendMessage}
				readonly={disabled || !canSendMessage}
				maxlength="200"
			/>
		</div>

		<button
			id="message-input-send-button"
			on:click={handleSend}
			disabled={disabled || !canSendMessage || (!messageText.trim() && fileUploadStates.length === 0) || hasValidationError || isUploading}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors {messageText.trim() ||
			fileUploadStates.length > 0
				? canSendMessage && !disabled && !hasValidationError && !isUploading
					? 'bg-blue-500 text-white shadow-sm hover:bg-blue-600'
					: 'cursor-not-allowed bg-gray-100 text-gray-400'
				: 'cursor-not-allowed bg-gray-100 text-gray-400'}"
			title={t('chat_center_send_message')}
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
				/>
			</svg>
		</button>
	</div>
</div>

<input
	id="message-input-file-input"
	bind:this={fileInput}
	type="file"
	multiple
	on:change={handleFileSelect}
	class="hidden"
	accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.ico,.mp4,.mp3,.wav,.m4a,.avi,.mov,.wmv,.flv,.webm,.ogg,.3gp,.zip,.rar,.7z,.tar,.gz,.bz2,.json,.xml,.yaml,.yml,.sql,.log"
	disabled={disabled || !canSendMessage}
/>

<style>
	textarea {
		overflow-y: auto;
		line-height: 1.5;
	}

	textarea:focus {
		resize: none;
	}

	.relative {
		min-height: 60px;
	}

	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.suggestion-dropdown {
		animation: slideUp 0.2s ease-out;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.suggestion-dropdown::-webkit-scrollbar {
		width: 6px;
	}

	.suggestion-dropdown::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}

	.suggestion-dropdown button:focus {
		outline: none;
	}

	.suggestion-dropdown button {
		transition: all 0.15s ease-in-out;
	}

	.suggestion-dropdown button.selected {
		background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
		border-color: #3b82f6;
		box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
		transform: translateY(-1px);
	}
</style>
