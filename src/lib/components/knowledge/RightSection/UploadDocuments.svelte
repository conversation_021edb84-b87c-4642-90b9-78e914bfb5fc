<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import {
		Heading,
		P,
		Card,
		Select,
		Label,
		Fileupload,
		Textarea,
		Datepicker,
		Button,
		Spinner,
		Toast
	} from 'flowbite-svelte';
	import {
		FolderOpenSolid,
		FileSolid,
		FileLinesSolid,
		CalendarMonthSolid,
		ImageSolid,
		UploadOutline,
		BullhornSolid,
		UserHeadsetSolid,
		FileImageSolid,
		TrashBinSolid,
		CloseCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';
	import { TimeoutError } from '$src/lib/api/client/errors';
	import { toastStore } from '$lib/stores/toastStore';

	let categorySelect = '';
	let categories = [
		{ value: 'CUSTOMER_SUPPORT', name: 'Customer Support' },
		{ value: 'PROMOTION', name: 'Promotion' },
		{ value: 'PRODUCT', name: 'Product' }
	];

	let productTypeSelect = '';
	let productTypes = [
		{ value: 1, name: 'CAR' },
		{ value: 2, name: 'COMPULSORY_MOTOR' },
		{ value: 3, name: 'HEALTH_ACCIDENT_TRAVEL' },
		{ value: 4, name: 'BUSINESS' },
		{ value: 5, name: 'HOME' },
		{ value: 6, name: 'SHIPPING' },
		{ value: 7, name: 'CANCER' },
		{ value: 8, name: 'CYBER' }
	];

	let startDate = null;
	let endDate = null;

	let uploadedFile: File | null = null;
	let uploadedImageFile: File | null = null;

	let loading = false;
    
	export let user_role = 'Agent';
	const unique_role_string = 'AIBL_Salmate_role';
	let check_user_role = `${user_role}+${unique_role_string}`;

	export let documents = [];

	// Reactive statements for form validation
	$: hasPermission = check_user_role === `Supervisor+${unique_role_string}` || check_user_role === `Admin+${unique_role_string}`;
	$: hasCategory = categorySelect !== '';
	$: hasFile = uploadedFile !== null;
	$: hasProductType = categorySelect !== 'PRODUCT' || productTypeSelect !== '';
	$: hasStartDate = categorySelect !== 'PROMOTION' || startDate !== null;
	$: hasEndDate = categorySelect !== 'PROMOTION' || endDate !== null;
	$: hasImageFile = categorySelect !== 'PRODUCT' || uploadedImageFile !== null;
	
	// Combined validation - all required fields must be valid
	$: isFormValid = hasPermission && hasCategory && hasFile && hasProductType && hasStartDate && hasEndDate && hasImageFile && !loading;

	// File handling logic
	function handleDocumentEdit(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			uploadedFile = target.files[0];
		}
	}

	const handleImageFileChange = (event: Event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			uploadedImageFile = target.files[0];
		}
	};

	// const templateDownloadUrls = [
	// 	'**********************************************************************************************?sp=r&st=2025-03-03T14:14:16Z&se=2026-03-05T22:14:16Z&spr=https&sv=2022-11-02&sr=b&sig=WFLRUVvGKtPLfT8Jm974PqtEmgEOSv4Yt7lHEUQlnrs%3D',
	// 	'**********************************************************************************************?sp=r&st=2025-03-03T14:19:40Z&se=2026-03-05T22:19:40Z&spr=https&sv=2022-11-02&sr=b&sig=s6X3XKiz9thLg0jE0T0wCfRFzvS%2BULhbtb5pxfwU09A%3D',
	// 	'*********************************************************************************************?sp=r&st=2025-03-03T14:08:39Z&se=2026-03-05T22:08:39Z&spr=https&sv=2022-11-02&sr=b&sig=b8uKeqHhq4VCTQml1uvnVRt907pZnCJG1jEWeSGYvG8%3D',
	// 	'*************************************************************************************?sp=r&st=2025-03-03T14:20:35Z&se=2026-03-05T22:20:35Z&spr=https&sv=2022-11-02&sr=b&sig=42eMUy9TGlELn6JoaGkNgBknrN%2FO1pDITZhGjZdn7yg%3D',
	// 	'************************************************************************************?sp=r&st=2025-03-03T14:20:15Z&se=2026-03-05T22:20:15Z&spr=https&sv=2022-11-02&sr=b&sig=dRaG7iIYw8%2BZ1N4Jkqfm9LjtDXjgvIcNiaBsizN77Sg%3D'
	// ];

	// Download function for templates
	async function downloadFile(idx) {
		const url = templateDownloadUrls[idx]; // Update with your actual endpoint

		try {
			const response = await fetch(url);
			if (!response.ok) throw new Error('Failed to fetch file');

			const blob = await response.blob();
			const link = document.createElement('a');
			link.href = URL.createObjectURL(blob);
			link.download = filename;
			link.click();
		} catch (error) {
			console.error('Error downloading file:', error);
		}
	}

	// Reference to the hidden section
	let hiddenSection;

	// Scroll to the hidden section when categorySelect changes
	$: if (categorySelect && hiddenSection) {
		hiddenSection.scrollIntoView({ behavior: 'smooth', block: 'end' });
	}


</script>

<!-- Template Files -->
<!-- <Heading tag="h3" class="mb-4" customSize="text-2xl font-bold">Template Files</Heading>
<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
	<div>
		<Heading tag="h3" class="mb-4" customSize="text-lg font-bold">
			Customer Support Template
		</Heading>
		<div class="flex gap-4">
			<Card href="{templateDownloadUrls[0]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(0)}>
				<img src="/images/icon-doc.png" alt="DOC Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[1]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(1)}>
				<img src="/images/icon-xlsx.png" alt="XLSX Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[2]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(2)}>
				<img src="/images/icon-csv.png" alt="CSV Icon" class="h-10 w-10" />
			</Card>
		</div>
	</div>
	<div>
		<Heading tag="h3" class="mb-4" customSize="text-lg font-bold">Product Template</Heading>
		<div class="flex gap-4">
			<Card href="{templateDownloadUrls[3]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(3)}>
				<img src="/images/icon-xlsx.png" alt="XLSX Icon" class="h-10 w-10" />
			</Card>

			<Card href="{templateDownloadUrls[4]}" class="flex w-24 items-center justify-center" on:click={() => downloadFile(4)}>
				<img src="/images/icon-csv.png" alt="CSV Icon" class="h-10 w-10" />
			</Card>
		</div>
	</div>
</div> -->

<!-- <Heading tag="h1" class="mb-6 flex justify-center" customSize="text-4xl font-bold">
	Upload Documents
</Heading> -->

<!-- Template Files -->
<!-- <Heading tag="h3" class="mb-4 mt-4" customSize="text-2xl font-bold">{t('upload_title')}</Heading> -->

<form
	method="POST"
	enctype="multipart/form-data"
	action="?/upload_file"
	use:enhance={() => {
		loading = true;

		return async ({ update, result }) => {
			if (result.type === 'success' || result.status === 200) {
				// Reset form fields after successful upload
				categorySelect = '';
				productTypeSelect = '';
				startDate = null;
				endDate = null;
				uploadedFile = null;
				uploadedImageFile = null;
				
				await update();
				toastStore.add(t('note_upload_success'), 'success');
			} else if (result.type === 'failure') {
				toastStore.add(result.data?.error || 'Upload failed', 'error');
			}

			loading = false;
		};
	}}
>
	<Label for="category-select" class="mb-2 flex gap-2">
		<FolderOpenSolid /> {t('select_category')}
	</Label>
	<Select id="category-select" items={categories} bind:value={categorySelect} />
	<input type="hidden" name="category" value={categorySelect} />

	{#if categorySelect !== ''}
		<div bind:this={hiddenSection}>
			<div class="mt-10 grid grid-cols-1 gap-4 md:grid-cols-2">
				<div class="p-4">
					<Label for="file-upload" class="flex gap-2 pb-2">
						<FileSolid />{t('upload_file')}
					</Label>

					<Fileupload
						id="file-upload"
						name="file"
						type="file"
						class="mb-2 [h-42px] py-0"
						on:change={handleDocumentEdit}
						required
					/>

					<div class="bg-orange-50 p-5 text-orange-700 dark:bg-orange-900 dark:text-orange-200">
						<Heading tag="h6" class="text-md mb-2 font-bold text-orange-700">{t('note')}:</Heading>
						<P class="text-orange-700">
							{#if categorySelect === 'CUSTOMER_SUPPORT'}
                                {t('note_supported_format_customer')}
							{:else if categorySelect === 'PROMOTION'}
                                {t('note_supported_format_promotion')}
							{:else if categorySelect === 'PRODUCT'}
                                {t('note_supported_format_product')}
							{/if}
						</P>
					</div>

					{#if categorySelect === 'PRODUCT'}
						<div class="mt-5">
							<Label for="product-type-select" class="mb-2 flex gap-2">
								<FolderOpenSolid /> {t('note_product_type_label')}
							</Label>
							<Select
								id="product-type-select"
								items={productTypes}
								bind:value={productTypeSelect}
								placeholder={t('note_product_type_placeholder')}
							/>
							<input
								type="hidden"
								name="selectedProductType"
								value={productTypeSelect ? productTypes[productTypeSelect - 1]['name'] : ''}
								required
							/>
						</div>
					{/if}
				</div>

				<div class="p-4">
					{#if categorySelect === 'PROMOTION'}
						<!-- <div class="mb-5 grid grid-cols-1 gap-4 md:grid-cols-2"> -->
						<div class="mb-5 flex flex-col gap-2">
							<div>
								<Label class="flex gap-2 pb-2">
									<CalendarMonthSolid />{t('start_date')}
								</Label>
								<Datepicker bind:value={startDate} placeholder={t('start_date')} required />
								<input
									type="hidden"
									name="start_date"
									value={startDate ? startDate.toISOString().split('T')[0] : ''}
								/>
							</div>
							<div>
								<Label class="flex gap-2 pb-2">
									<CalendarMonthSolid />{t('end_date')}
								</Label>
								<Datepicker bind:value={endDate} placeholder={t('end_date')} required />
								<input
									type="hidden"
									name="end_date"
									value={endDate ? endDate.toISOString().split('T')[0] : ''}
								/>
							</div>
						</div>
					{:else if categorySelect === 'PRODUCT'}
						<div class="mb-5">
							<Label for="file-upload" class="flex gap-2 pb-2">
								<ImageSolid />Select Image
							</Label>

							<Fileupload
								id="image-upload"
								name="image_file"
								type="file"
								accept="image/*"
								class="mb-2 [h-42px] py-0"
								on:change={handleImageFileChange}
								required
							/>

							<div class="bg-orange-50 p-5 text-orange-700 dark:bg-orange-900 dark:text-orange-200">
								<Heading tag="h6" class="text-md mb-2 font-bold text-orange-700">{t('note')}:</Heading>
								<P class="text-orange-700">{t('note_supported_image')}</P>
							</div>
						</div>
					{/if}
					<div>
						<Label for="textarea-id" class="mb-2 flex gap-2">
							<FileLinesSolid /> {t('description_upload_file')}
						</Label>
						<Textarea
							id="textarea-id"
							placeholder={t('note_message_placeholder')}
							rows={3}
							name="description"
						/>
					</div>
				</div>
			</div>
			<div class="flex items-center justify-left gap-4 p-4">
				<Button
					type="submit"
					color="blue"
					disabled={!isFormValid}
					class="disabled:cursor-not-allowed disabled:opacity-20"
				>
					{#if loading}
						<Spinner class="me-3" size="4" color="white" /> {t('uploading')}
					{:else}
						<UploadOutline class="mr-2 h-4 w-4" />
						{t('upload_button')}
					{/if}
				</Button>

				<div class="bg-gray-100text-gray-700">
					<P class="text-gray-700">
						<span class="text-md mb-2 font-bold text-gray-700">{t('note')}:</span> {t('note_admin_only')}
					</P>
				</div>
			</div>
		</div>
	{/if}
</form>
    