<script lang="ts">
    import { t } from '$src/lib/stores/i18n';
    import { displayDate, getUserWorkSchedule } from '$lib/utils';
    import { AccordionItem, Accordion } from "flowbite-svelte";
    import { ArchiveSolid, FileLinesSolid, UserHeadsetSolid, BullhornSolid, FileImageSolid, TrashBinSolid, FolderOpenSolid, AdjustmentsHorizontalSolid, ChevronDownOutline, SearchOutline, CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';
    import {
      Card,
      Button,
      Dropdown,
      Checkbox,
      Input,
      Label,
      Heading,
      Table,
      TableBody,
      TableBodyCell,
      TableBodyRow,
      TableHead,
      TableHeadCell
    } from 'flowbite-svelte';

    // API Integration State
    let availableApiSearchTerm = ''; // For searching available APIs
    let connectedApiSearchTerm = ''; // For searching connected APIs
    let selectedCategory = 'all';
    
    // Filter state for dropdowns
    let selectedCategoryFilter = new Set(['All']);
    let selectedStatusFilter = new Set(['All']);
    let selectedPermissionsFilter = new Set(['All']);
    
    // Sorting state
    let sortColumn = '';
    let sortDirection = 'asc';
    
    // Filter options - เรียงสถานะให้ connected ขึ้นมาก่อน
    let categoryFilterOptions = ['All', 'API', 'Database', 'CRM'];
    let statusFilterOptions = ['All', 'connected', 'disconnected'];
    let permissionsFilterOptions = ['All', 'admin', 'supervisor', 'agent'];

    // API Categories
    let apiCategories = [
      { value: 'CRM', name: 'CRM' },
      { value: 'Databases', name: 'Databases' }
    ];

    // Available APIs for connection (shown in cards)
    let availableApis = [
      {
        id: 'salesforce',
        name: 'Salesforce',
        description: 'Cloud-based customer relationship management (CRM) platform that helps businesses manage sales...',
        category: 'CRM',
        color: 'blue',
        iconName: 'salesforce'
      },
      {
        id: 'mongodb',
        name: 'MongoDB',
        description: 'MongoDB is an open source NoSQL database management program.',
        category: 'Databases',
        color: 'blue',
        iconName: 'mongodb'
      },
      {
        id: 'mysql',
        name: 'MySQL',
        description: 'MySQL is an open-source relational database management system.',
        category: 'Databases',
        color: 'blue',
        iconName: 'mysql'
      },
      {
        id: 'postgresql',
        name: 'PostgreSQL',
        description: 'PostgreSQL is a free and open-source relational database management system emphasizing extensibility...',
        category: 'Databases',
        color: 'blue',
        iconName: 'postgresql'
      },
      {
        id: 'azure_sql',
        name: 'Microsoft Azure SQL Database',
        description: 'Build apps that scale with managed and intelligent SQL database in the cloud.',
        category: 'Databases',
        color: 'blue',
        iconName: 'azure-sql'
      }
    ];

    // Connected APIs (shown in table) - can have multiple connections of same type
    let connectedApis = [
      {
        id: 1,
        name: 'Soho API',
        category: 'CRM',
        connected_date: '01/10/2024',
        permissions: ['admin', 'supervisor'],
        status: 'connected',
        iconName: 'salesforce'
      },
      {
        id: 2,
        name: 'Salesforce API',
        category: 'CRM',
        connected_date: '01/12/2024',
        permissions: ['admin'],
        status: 'connected',
        iconName: 'salesforce'
      },
      {
        id: 3,
        name: 'Claims Processing API',
        category: 'API',
        connected_date: '01/14/2024',
        permissions: ['admin', 'supervisor', 'agent'],
        status: 'disconnected',
        iconName: 'postgresql'
      },
      {
        id: 4,
        name: 'MySQL Production DB',
        category: 'Database',
        connected_date: '01/15/2024',
        permissions: ['admin'],
        status: 'connected',
        iconName: 'mysql'
      },
      {
        id: 5,
        name: 'MySQL Staging DB',
        category: 'Database',
        connected_date: '01/16/2024',
        permissions: ['admin', 'supervisor'],
        status: 'disconnected',
        iconName: 'mysql'
      }
    ];

    // Filtered APIs for cards (uses availableApiSearchTerm)
    $: filteredApis = availableApis.filter(api => {
      const matchesSearch = availableApiSearchTerm === '' || 
        api.name.toLowerCase().includes(availableApiSearchTerm.toLowerCase()) ||
        api.description.toLowerCase().includes(availableApiSearchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || api.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });

    // Filtered connections for table (uses connectedApiSearchTerm) - เรียงจากใหม่ไปเก่า และ connected ขึ้นมาก่อน
    $: filteredConnections = connectedApis
        .filter(connection => {
            const matchesSearch = connectedApiSearchTerm === '' || 
                connection.name.toLowerCase().includes(connectedApiSearchTerm.toLowerCase());
            
            const matchesCategory = selectedCategoryFilter.has('All') || selectedCategoryFilter.has(connection.category);
            
            const matchesStatus = selectedStatusFilter.has('All') || selectedStatusFilter.has(connection.status);
            
            const matchesPermissions = selectedPermissionsFilter.has('All') || 
                Array.from(selectedPermissionsFilter).some(permission => 
                permission !== 'All' && connection.permissions.includes(permission)
                );
            
            return matchesSearch && matchesCategory && matchesStatus && matchesPermissions;
        })
        .sort((a, b) => {
            // Apply custom sorting if column is selected
            if (sortColumn === 'id') {
                const compareValue = sortDirection === 'asc' ? a.id - b.id : b.id - a.id;
                return compareValue;
            } else if (sortColumn === 'status') {
                if (sortDirection === 'asc') {
                    return a.status.localeCompare(b.status);
                } else {
                    return b.status.localeCompare(a.status);
                }
            } else if (sortColumn === 'connected_date') {
                const dateA = new Date(a.connected_date.split('/').reverse().join('-'));
                const dateB = new Date(b.connected_date.split('/').reverse().join('-'));
                if (sortDirection === 'asc') {
                    return dateA.getTime() - dateB.getTime(); // เก่าไปใหม่
                } else {
                    return dateB.getTime() - dateA.getTime(); // ใหม่ไปเก่า
                }
            }
            
            // Default sorting: connected ขึ้นมาก่อน แล้วเรียงตามวันที่ใหม่ไปเก่า
            if (a.status !== b.status) {
                return a.status === 'connected' ? -1 : 1;
            }
            
            const dateA = new Date(a.connected_date.split('/').reverse().join('-'));
            const dateB = new Date(b.connected_date.split('/').reverse().join('-'));
            return dateB.getTime() - dateA.getTime();
        });

    // Filter functions
    function toggleCategoryFilter(category) {
      if (category === 'All') {
        selectedCategoryFilter = new Set(['All']);
      } else {
        if (selectedCategoryFilter.has(category)) {
          selectedCategoryFilter.delete(category);
        } else {
          selectedCategoryFilter.add(category);
          selectedCategoryFilter.delete('All');
        }
        if (selectedCategoryFilter.size === 0) {
          selectedCategoryFilter.add('All');
        }
        selectedCategoryFilter = selectedCategoryFilter;
      }
    }

    function toggleStatusFilter(status) {
      if (status === 'All') {
        selectedStatusFilter = new Set(['All']);
      } else {
        if (selectedStatusFilter.has(status)) {
          selectedStatusFilter.delete(status);
        } else {
          selectedStatusFilter.add(status);
          selectedStatusFilter.delete('All');
        }
        if (selectedStatusFilter.size === 0) {
          selectedStatusFilter.add('All');
        }
        selectedStatusFilter = selectedStatusFilter;
      }
    }

    function togglePermissionsFilter(permission) {
      if (permission === 'All') {
        selectedPermissionsFilter = new Set(['All']);
      } else {
        if (selectedPermissionsFilter.has(permission)) {
          selectedPermissionsFilter.delete(permission);
        } else {
          selectedPermissionsFilter.add(permission);
          selectedPermissionsFilter.delete('All');
        }
        if (selectedPermissionsFilter.size === 0) {
          selectedPermissionsFilter.add('All');
        }
        selectedPermissionsFilter = selectedPermissionsFilter;
      }
    }

    function resetApiFilters() {
      selectedCategoryFilter = new Set(['All']);
      selectedStatusFilter = new Set(['All']);
      selectedPermissionsFilter = new Set(['All']);
      connectedApiSearchTerm = '';
    }

    // Search handlers - separate for both
    function handleAvailableApiSearch(event) {
      availableApiSearchTerm = event.target.value;
    }

    function handleConnectedApiSearch(event) {
      connectedApiSearchTerm = event.target.value;
    }

    // Category filter
    function filterByCategory(category) {
      selectedCategory = category;
    }

    // Delete connection
    function deleteConnection(connectionId) {
      connectedApis = connectedApis.filter(conn => conn.id !== connectionId);
    }

    // Sorting function
    function sortTable(column) {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }
    }
</script>

<div class="space-y-4">
    <!-- AI Developer Toolkit Section -->
    <Accordion>
    <AccordionItem class="hover:bg-gray-50 rounded-lg">
        <span slot="header" class="p-2 grid grid-cols-[auto_1fr] gap-3 items-center">
        <ArchiveSolid class="w-5 h-5" />
        <div class="flex flex-col">
            <div class="font-semibold text-sm">{t('api_integration')}</div>
            <div class="text-sm text-gray-600">{t('api_description')}</div>
        </div>
        </span>

        <div class="p-4 space-y-6">
        <!-- Categories Sidebar and Search -->
        <div class="flex gap-6">
            <!-- Left Sidebar -->
            <div class="w-48 space-y-2">
            <div class="text-sm font-medium text-gray-700 mb-3">{t('category')}</div>
            <button
                class="{selectedCategory === 'all' 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} 
                w-full text-left px-3 py-2 rounded-lg text-sm transition-colors"
                on:click={() => filterByCategory('all')}
            >
                {t('all')}
            </button>
            {#each apiCategories as category}
                <button
                class="{selectedCategory === category.value 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} 
                    w-full text-left px-3 py-2 rounded-lg text-sm transition-colors"
                on:click={() => filterByCategory(category.value)}
                >
                {category.name}
                </button>
            {/each}
            </div>

            <!-- Right Content Area -->
            <div class="flex-1">
            <!-- Search Bar for Available APIs -->
            <div class="relative mb-6">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                </div>
                <input
                type="text"
                placeholder={t('search_apis')}
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                bind:value={availableApiSearchTerm}
                on:input={handleAvailableApiSearch}
                />
            </div>

            <!-- API Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {#each filteredApis as api}
                <Card class="p-4 hover:shadow-md transition-shadow duration-200 border border-gray-200">
                    <div class="flex items-start space-x-3">
                    <!-- API Icon -->
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                        <img src="/mcp_logo/{api.iconName}-icon.png" alt="{api.name}" class="w-7 h-7" />
                        </div>
                    </div>
                    
                    <!-- API Details -->
                    <div class="flex-1 min-w-0">
                        <h4 class="text-base font-semibold text-gray-900 mb-1">{api.name}</h4>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">{api.description}</p>
                        
                        <!-- Category Badge -->
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-{api.color}-100 text-{api.color}-700">
                        {api.category}
                        </span>
                    </div>
                    </div>
                </Card>
                {/each}
            </div>

            <!-- No Results -->
            {#if filteredApis.length === 0}
                <div class="text-center py-12">
                <FolderOpenSolid class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">{t('no_found_api')}</h3>
                </div>
            {/if}
            </div>
        </div>
        </div>
    </AccordionItem>
    </Accordion>


    <!-- Connected APIs Table -->
    <Heading tag="h3" class="text-lg font-semibold text-gray-900 mb-4">{t('active_connections')}</Heading>

    <!-- Filter Section -->
    <div class="mt-4 mb-4 grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <!-- Left side - Filter Buttons -->
        <div class="col-span-2">
            <div id="api-filter-buttons" class="flex flex-wrap gap-3">
                <!-- Category Filter -->
                <div>
                    <Button
                        id="api-category-filter-button"
                        color={!selectedCategoryFilter.has('All') ? 'dark' : 'none'}
                        class={`${!selectedCategoryFilter.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
                    >
                        <AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
                        <span>{t('filter_category')}</span>
                        <ChevronDownOutline class="h-3 w-3" />
                    </Button>
                    <Dropdown id="api-category-filter-dropdown" class="w-44 p-2 shadow-lg">
                        {#each categoryFilterOptions as category}
                            <Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
                                <Checkbox
                                    id="api-category-filter-{category.toLowerCase()}"
                                    checked={selectedCategoryFilter.has(category)}
                                    on:change={() => toggleCategoryFilter(category)}
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                <span class="ml-2 text-sm">
                                    {category === 'All' ? t('filter_all') : category}
                                </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Status Filter - ปรับให้เหมือนตัวอย่างที่ให้มา -->
                <div>
                    <Button
                        id="api-status-filter-button"
                        color={!selectedStatusFilter.has('All') ? 'dark' : 'none'}
                        class={`${!selectedStatusFilter.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
                        data-testid="status-filter-button"
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4 mr-1" />
                        <span>{t('status')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown id="api-status-filter-dropdown" class="w-44 p-2 shadow-lg">
                        {#each statusFilterOptions as status}
                            <Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
                                <Checkbox
                                    id="api-status-filter-{status.toLowerCase()}"
                                    checked={selectedStatusFilter.has(status)}
                                    on:change={() => toggleStatusFilter(status)}
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                <span class="ml-2 text-sm">
                                    {status === 'All' ? t('filter_all') : t(status)}
                                </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Permissions Filter -->
                <div>
                    <Button
                        id="api-permissions-filter-button"
                        color={!selectedPermissionsFilter.has('All') ? 'dark' : 'none'}
                        class={`${!selectedPermissionsFilter.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
                    >
                        <AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
                        <span>{t('access_permissions')}</span>
                        <ChevronDownOutline class="h-3 w-3" />
                    </Button>
                    <Dropdown id="api-permissions-filter-dropdown" class="w-44 p-2 shadow-lg">
                        {#each permissionsFilterOptions as permission}
                            <Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
                                <Checkbox
                                    id="api-permissions-filter-{permission.toLowerCase()}"
                                    checked={selectedPermissionsFilter.has(permission)}
                                    on:change={() => togglePermissionsFilter(permission)}
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                <span class="ml-2 text-sm">
                                    {permission === 'All' ? t('filter_all') : t(permission)}
                                </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Reset Filter -->
                <Button
                    id="api-reset-filters-button"
                    color="none"
                    on:click={resetApiFilters}
                    class="w-auto border shadow-md hover:bg-gray-100"
                >
                    {t('filter_reset')}
                </Button>
            </div>
        </div>
        <!-- Right side - Search Bar for Connected APIs -->
        <div class="col-span-1">
            <div id="api-search-container" class="relative w-full shadow-md">
                <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <SearchOutline class="h-5 w-5 text-gray-500" />
                </div>
                <Input
                    type="text"
                    id="api-search-input"
                    placeholder={t('search_connections')}
                    bind:value={connectedApiSearchTerm}
                    on:input={handleConnectedApiSearch}
                    class={`block w-full rounded-lg border bg-white py-2.5 pl-10 focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 ${connectedApiSearchTerm ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
                />
            </div>
        </div>
    </div>

    <!-- Table Content using Flowbite Table Components -->
    <div class="overflow-x-auto">
        <Table>
            <TableHead>
                <TableHeadCell class="w-16 cursor-pointer" on:click={() => sortTable('id')}>
                    <div class="flex items-center justify-center">
                        {t('table_no')}
                        {#if sortColumn === 'id'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <TableHeadCell>{t('name')}</TableHeadCell>
                <TableHeadCell>{t('category')}</TableHeadCell>
                <TableHeadCell>{t('access_permissions')}</TableHeadCell>
                <TableHeadCell class="cursor-pointer" on:click={() => sortTable('status')}>
                    <div class="flex items-center">
                        {t('status')}
                        {#if sortColumn === 'status'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <TableHeadCell class="cursor-pointer" on:click={() => sortTable('connected_date')}>
                    <div class="flex items-center">
                        {t('connected_date')}
                        {#if sortColumn === 'connected_date'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
            </TableHead>
            <TableBody>
                {#each filteredConnections as connection}
                <TableBodyRow class="hover:bg-gray-50">
                    <TableBodyCell class="text-center text-sm font-medium text-gray-500">
                        {connection.id}
                    </TableBodyCell>
                    <TableBodyCell>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8">
                                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                                    <img src="/mcp_logo/{connection.iconName}-icon.png" alt="{connection.name}" class="w-7 h-7" />
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{connection.name}</div>
                            </div>
                        </div>
                    </TableBodyCell>
                    <TableBodyCell>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {connection.category}
                        </span>
                    </TableBodyCell>
                    <TableBodyCell>
                        <div class="flex flex-wrap gap-1">
                            {#each connection.permissions as permission}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700">
                                    {t(permission)}
                                </span>
                            {/each}
                        </div>
                    </TableBodyCell>
                    <TableBodyCell>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                            {connection.status === 'connected'
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'}">
                            {t(connection.status)}
                        </span>
                    </TableBodyCell>
                    <TableBodyCell id="api-connections-table-cell-connected-date-{connection.id}">
                        <div id="api-connections-connected-date-{connection.id}" class="{connection.status !== 'connected' ? 'text-gray-400' : ''}">
                            <div>{displayDate(connection.connected_date).date}</div>
                            <div>{displayDate(connection.connected_date).time}</div>
                        </div>
                    </TableBodyCell>
                </TableBodyRow>
                {/each}
            </TableBody>
        </Table>

        <!-- Empty State for Table -->
        {#if filteredConnections.length === 0}
            <div class="text-center py-12">
                <div class="text-gray-500 mb-2">{t('no_matching_connections')}</div>
            </div>
        {/if}
    </div>
</div>